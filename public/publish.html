<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <title>西部钻探AI - WPS加载项</title>
        <meta name="keywords" content="西部钻探AI,WPS加载项,文本润色,智能助手,昆仑大模型" />
        <meta name="description" content="西部钻探AI - 专业的WPS文档智能处理工具" />
        <meta name="author" content="西部钻探工程有限公司" />
        <!--  FavIcon  -->
        <link rel="icon" type="image/png" sizes="32x32" href="images/icon.png">
        <!-- CSS -->
        <link rel="stylesheet" href="static/css/bootstrap.css">
        <link rel="stylesheet" href="static/css/animated.css">
        <link rel="stylesheet" href="static/css/lineicons.css">
        <link rel="stylesheet" href="static/css/magnific-popup.css">
        <link rel="stylesheet" href="static/css/owl.carousel.min.css">
        <link rel="stylesheet" href="static/css/style.css">
        <link href="static/css/b2d1dcf2b01f4a95a41bc8eff54f1d8d.css" rel="stylesheet">
        <style>
            /* 自定义样式 */
            .hero-bg-graphics {
                background-image: url('images/xdecai.jpg');
                background-size: cover;
                background-position: center center;
                background-repeat: no-repeat;
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: -1;
            }

            /* logo样式 */
            .logo-container {
                position: absolute;
                top: 40px;
                left: 50px;
                z-index: 1000;
            }

            .logo-container img {
                width: 25%; 
                height: auto;
                /* 已取消阴影效果 */
            }

            /* 按钮容器样式 */
            .buttons-container {
                display: flex;
                justify-content: flex-start;
                align-items: flex-end; /* 使按钮底部对齐 */
                margin-top: 100px; /* 从30px增加到130px，向下移动100px */
                margin-left: -50px; /* 与标题保持一致的左边距 */
                z-index: 1000;
            }

            /* 安装按钮样式 */
            .tech-button {
                width: 160px;
                height: 55px;
                background-image: url('images/install.png');
                background-size: contain;
                background-repeat: no-repeat;
                background-position: center;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                color: transparent;
                font-size: 0;
                transition: transform 0.3s ease;
                margin-right: 20px;
            }

            .tech-button:hover {
                transform: scale(1.05);
            }

            /* 下载按钮通用样式 */
            .download-button {
                cursor: pointer;
                transition: transform 0.3s ease;
                border: none;
                background: transparent;
                padding: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 20px;
            }

            .download-button img {
                width: auto;
                height: 100%;
                max-width: 100%;
                display: block;
                margin: 0 auto;
                object-fit: contain;
            }

            .download-button:hover {
                transform: scale(1.05);
            }

            /* WPS下载按钮位置和大小 */
            .wps-button {
                width: 130px; /* 缩小宽度 */
                height: 50px; /* 与安装按钮相同高度 */
                background-size: contain;
                background-repeat: no-repeat;
                background-position: center;
            }

            /* README下载按钮位置和大小 */
            .readme-button {
                width: 80px;  /* 缩小宽度 */
                height: 45px; /* 与安装按钮相同高度 */
                background-size: contain;
                background-repeat: no-repeat;
                background-position: center;
                position: relative;
            }

            /* 隐藏原有界面元素 */
            .addonList, .addonItem, .addonItemTitle, .divTitle, .ClearAll {
                display: none;
            }

            /* 功能特点卡片样式 */
            .feature-card {
                background-color: rgba(255, 255, 255, 0.9);
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 20px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }

            .feature-icon {
                font-size: 40px;
                color: #417ff9;
                margin-bottom: 15px;
            }

            /* 调整页面内容区域 */
            .page-content {
                min-height: 100vh;
            }

            /* 调整文字颜色和位置 */
            .hero-txt {
                padding-left: 0; /* 移除左内边距 */
                margin-left: -200px; /* 大幅向左移动 */
            }

            .hero-txt h1, .hero-txt h5 {
                color: #fff;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
                margin-left: -50px; /* 标题文字单独向左移动更多 */
            }

            /* 隐藏不需要的部分 */
            #price, #testimonial {
                display: none;
            }

            /* 放大02.png图片 */
            .hero-img {
                transform: scale(1.5); /* 放大1.5倍 */
                margin-top: -40px; /* 向上移动一些 */
                margin-left: -40px; /* 向左移动一些，确保不覆盖左侧内容 */
                overflow: visible; /* 允许内容溢出容器 */
                position: relative; /* 设置相对定位 */
                z-index: 1; /* 确保图片在适当的层级 */
                transition: transform 0.5s ease; /* 添加过渡效果 */
            }

            .hero-img img {
                transition: all 0.5s ease;
                transform: translateY(0);
            }

            .hero-img:hover img {
                transform: translateY(-5px); /* 鼠标悬停时轻微上浮 */
                filter: drop-shadow(0 20px 30px rgba(0, 0, 0, 0.8)) !important; /* 鼠标悬停时阴影加深 */
            }

            /* 保留原有的安装/卸载按钮样式，但隐藏起来 */
            .addonItemButton {
                padding: 4px 8px;
                background-color: #417ff9;
                display: none;
                cursor: pointer;
                box-sizing: border-box;
                border-radius: 4px;
                text-align: center;
                color: #fff;
            }
        </style>
    </head>
    <body data-spy="scroll" data-target="#scrollspy" data-offset="61" onload="LoadAddons()">
        <div id="loader-wrapper">
            <div id="loader">
                <div class="cssload-spin-box"></div>
            </div>
        </div>

        <!-- 隐藏的WPS加载项元素 -->
        <div style="display:none;">
            <div class="divTitle">WPS加载项配置</div>
            <div class="addonList" id="addonList">
                <div class="addonItem addonItemTitle">
                    <div class="addonItemName1">加载项名称</div>
                    <div class="addonItemName2">类型</div>
                    <div class="addonItemName3">加载方式</div>
                    <div class="addonItemName4">URL</div>
                    <div class="addonItemName1">自定义域名</div>
                    <div class="addonItemName5">管理</div>
                    <div class="addonItemName6">状态</div>
                </div>
            </div>
            <div class="ClearAll" onclick="ClearAll()" id="ClearAll">禁用所有 WPS 加载项</div>
        </div>

        <div id="page-content" class="page-content demo-01">
            <!-- 主页开始 -->
            <section id="hero-01" class="hero full-screen py-6">
                <div class="hero-bg-graphics"></div>
                <!-- logo -->
                <div class="logo-container">
                    <img src="images/logop.png" alt="西部钻探AI Logo">
                </div>
                <div class="hero-center">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col-lg-6" style="position: relative; overflow: visible;">
                                <div class="hero-txt">
                                    <div>
                                        <!-- 主标题 -->
                                        <h1 class="font-weight-bold">西部钻探AI</h1>
                                        <h1 class="font-weight-bold">中油版WPS</h1>
                                        <h1 class="font-weight-bold">智能办公套件</h1>
                                        <!-- 主标题介绍 -->
                                        <h5 class="text-capitalize"><span>让文档处理更智能</span> <span class="element second-color" data-elements="把人工失误扔进回收站"></span></h5>

                                        <!-- 按钮容器 - 放在标题下方 -->
                                        <div class="buttons-container">
                                            <!-- 安装/卸载/更新按钮 -->
                                            <div id="tech-button-container"></div>

                                            <!-- WPS下载按钮 -->
                                            <button class="download-button wps-button" onclick="downloadWPS()">
                                                <img src="images/download.png" alt="下载最新版WPS" title="点击下载最新版WPS">
                                            </button>

                                            <!-- README下载按钮 -->
                                            <button class="download-button readme-button" onclick="downloadReadme()">
                                                <img src="images/instructions.png" alt="使用说明" title="点击下载使用说明">
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="hero-img">
                                    <img class="img-fluid" src="static/picture/02.png" alt="西部钻探AI" style="max-width: 120%; height: auto; filter: drop-shadow(0 15px 25px rgba(0, 0, 0, 0.6));" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 功能介绍 -->
            <section id="services" class="services py-6">
                <div class="services-bg-graphics">
                    <img src="static/picture/services-bg.png" alt="/">
                </div>
                <div class="container">
                    <div class="section-title text-center">
                        <h3 class="sub-title base-color mb-2">西部钻探AI</h3>
                        <h2 class="title text-dark">强大功能，提升效率</h2>
                    </div>
                    <div class="row mt-5">
                        <div class="col-lg-4 col-md-6 mt-4 wow fadeInUp" data-wow-duration="1.25s">
                            <div class="serviceBox">
                                <div class="service-icon">
                                    <img src="images/runse.png" alt="文本润色">
                                </div>
                                <h3>文本润色</h3>
                                <p>一键优化文档表达，修正语法错误，提升文档专业度和可读性</p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 mt-4 wow fadeInUp" data-wow-duration="1.5s">
                            <div class="serviceBox">
                                <div class="service-icon">
                                    <img src="images/custom-lang.png" alt="语言翻译">
                                </div>
                                <h3>语言翻译</h3>
                                <p>支持中英互译及多语言翻译，满足国际化文档需求</p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 mt-4 text-md-left wow fadeInUp" data-wow-duration="1.75s">
                            <div class="serviceBox">
                                <div class="service-icon">
                                    <img src="images/moban.png" alt="文档模板">
                                </div>
                                <h3>文档模板</h3>
                                <p>提供专业文档模板，包括专利交底书等，快速创建规范文档</p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 mt-4 text-md-left wow fadeInUp" data-wow-duration="2s">
                            <div class="serviceBox">
                                <div class="service-icon">
                                    <img src="images/assistant.png" alt="智能助手">
                                </div>
                                <h3>智能助手</h3>
                                <p>基于deepseek大模型的智能问答系统，解答文档编写相关问题</p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 mt-4 text-md-left wow fadeInUp" data-wow-duration="2.25s">
                            <div class="serviceBox">
                                <div class="service-icon">
                                    <img src="images/kuoxie.png" alt="文本扩写">
                                </div>
                                <h3>文本扩写</h3>
                                <p>将简短内容智能扩展为详细文字，节省写作时间</p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 mt-4 text-md-left wow fadeInUp" data-wow-duration="2.5s">
                            <div class="serviceBox">
                                <div class="service-icon">
                                    <img src="images/suoxie.png" alt="文本缩写">
                                </div>
                                <h3>文本缩写</h3>
                                <p>将冗长内容精简为简洁表达，保留核心信息</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 关于我们 -->
            <section id="about" class="about py-6">
                <div class="about-bg-graphics">
                    <img src="static/picture/about-bg.png" alt="/">
                </div>
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-lg-6 mb-4 mb-lg-0">
                            <img src="static/picture/about-01.jpg" alt="/" class="w-100">
                        </div>
                        <div class="col-lg-6">
                            <div class="section-title">
                                <h3 class="sub-title mb-2">西部钻探软件研发中心</h3>
                                <h2 class="title">关于我们</h2>
                            </div>
                            <p class="text-muted my-3">西部钻探AI是一款专为文档处理设计的智能工具，基于deepseek大模型，为用户提供高效、专业的文档处理服务。</p>
                            <p class="text-muted my-3">我们致力于通过人工智能技术，提升文档编写效率，减少人工失误，让文档处理更加智能化、规范化。</p>
                            <p class="text-muted my-3">产品支持文本润色、语言翻译、文档模板管理、智能问答等多种功能，满足各类文档处理需求。如需定制化开发，请联系西部钻探软件研发中心。</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 底部版权 -->
            <section class="w3l-copyright text-center" style="background-color: white !important;border-top:none !important;padding:0.6rem 0 !important;">
                <div class="container">
                    <h4 class="copy-footer-29" style="color: black;">Copyright &copy; 2025 · 西部钻探工程有限公司</h4>
                </div>
            </section>
            <!-- 返回顶部 -->
            <a href="javascript:void(0)" class="return-to-top"><i class="lni-chevron-up"></i></a>
        </div>
        <!--  JS -->
        <script src="static/js/jquery-3.5.1.min.js"></script>
        <script src="static/js/bootstrap.js"></script>
        <script src="static/js/jquery.easing.min.js"></script>
        <script src="static/js/typed.js"></script>
        <script src="static/js/wow.min.js"></script>
        <script src="static/js/jquery.countto.js"></script>
        <script src="static/js/isotope.pkgd.min.js"></script>
        <script src="static/js/jquery.magnific-popup.min.js"></script>
        <script src="static/js/owl.carousel.min.js"></script>
        <script src="static/js/roiton.js"></script>
    <!-- WPS加载项相关脚本 -->
    <script>
        function getHttpObj() {
            var httpobj = null;
            if (IEVersion() < 10) {
                try {
                    httpobj = new XDomainRequest();
                } catch (e1) {
                    httpobj = new createXHR();
                }
            } else {
                httpobj = new createXHR();
            }
            return httpobj;
        }
        //兼容IE低版本的创建xmlhttprequest对象的方法
        function createXHR() {
            if (typeof XMLHttpRequest != 'undefined') { //兼容高版本浏览器
                return new XMLHttpRequest();
            } else if (typeof ActiveXObject != 'undefined') { //IE6 采用 ActiveXObject， 兼容IE6
                var versions = [ //由于MSXML库有3个版本，因此都要考虑
                    'MSXML2.XMLHttp.6.0',
                    'MSXML2.XMLHttp.3.0',
                    'MSXML2.XMLHttp'
                ];

                for (var i = 0; i < versions.length; i++) {
                    try {
                        return new ActiveXObject(versions[i]);
                    } catch (e) {
                        //跳过
                    }
                }
            } else {
                throw new Error('您的浏览器不支持XHR对象');
            }
        }

        var fromCharCode = String.fromCharCode;
        // encoder stuff
        var cb_utob = function (c) {
            if (c.length < 2) {
                var cc = c.charCodeAt(0);
                return cc < 0x80 ? c :
                    cc < 0x800 ? (fromCharCode(0xc0 | (cc >>> 6)) +
                        fromCharCode(0x80 | (cc & 0x3f))) :
                        (fromCharCode(0xe0 | ((cc >>> 12) & 0x0f)) +
                            fromCharCode(0x80 | ((cc >>> 6) & 0x3f)) +
                            fromCharCode(0x80 | (cc & 0x3f)));
            } else {
                var cc = 0x10000 +
                    (c.charCodeAt(0) - 0xD800) * 0x400 +
                    (c.charCodeAt(1) - 0xDC00);
                return (fromCharCode(0xf0 | ((cc >>> 18) & 0x07)) +
                    fromCharCode(0x80 | ((cc >>> 12) & 0x3f)) +
                    fromCharCode(0x80 | ((cc >>> 6) & 0x3f)) +
                    fromCharCode(0x80 | (cc & 0x3f)));
            }
        };
        var re_utob = /[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g;
        var utob = function (u) {
            return u.replace(re_utob, cb_utob);
        };
        var _encode = function (u) {
            var isUint8Array = Object.prototype.toString.call(u) === '[object Uint8Array]';
            if (isUint8Array)
                return u.toString('base64')
            else
                return btoa(utob(String(u)));
        }

        if (typeof btoa !== 'function') btoa = func_btoa;

        function func_btoa(input) {
            var str = String(input);
            var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
            for (
                // initialize result and counter
                var block, charCode, idx = 0, map = chars, output = '';
                // if the next str index does not exist:
                //   change the mapping table to "="
                //   check if d has no fractional digits
                str.charAt(idx | 0) || (map = '=', idx % 1);
                // "8 - idx % 1 * 8" generates the sequence 2, 4, 6, 8
                output += map.charAt(63 & block >> 8 - idx % 1 * 8)
            ) {
                charCode = str.charCodeAt(idx += 3 / 4);
                if (charCode > 0xFF) {
                    throw new InvalidCharacterError("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");
                }
                block = block << 8 | charCode;
            }
            return output;
        }

        function encode(u, urisafe) {
            return !urisafe ?
                _encode(u) :
                _encode(String(u)).replace(/[+\/]/g, function (m0) {
                    return m0 == '+' ? '-' : '_';
                }).replace(/=/g, '');
        }

        function IEVersion() {
            var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
            var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器
            var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器
            var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
            if (isIE) {
                var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
                reIE.test(userAgent);
                var fIEVersion = parseFloat(RegExp["$1"]);
                if (fIEVersion == 7) {
                    return 7;
                } else if (fIEVersion == 8) {
                    return 8;
                } else if (fIEVersion == 9) {
                    return 9;
                } else if (fIEVersion == 10) {
                    return 10;
                } else {
                    return 6; //IE版本<=7
                }
            } else if (isEdge) {
                return 20; //edge
            } else if (isIE11) {
                return 11; //IE11
            } else {
                return 30; //不是ie浏览器
            }
        }

        /**
         * 生成guid的接口
         * @returns guid
         */
        function guid() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        /**
         * 自定义协议启动服务端
         * 默认不带参数serverId，linux未升级之前不要使用多用户
         */
        function InitWpsCloudSvr() {
            if (serverId == undefined)
                window.location.href = "ksoWPSCloudSvr://start=RelayHttpServer"//是否启动wps弹框
            else
                window.location.href = "ksoWPSCloudSvr://start=RelayHttpServer" + "&serverId=" + serverId //是否启动wps弹框
        }

        var serverId = getServerId();

        /**
         * 获取serverId的接口
         * @returns serverId
         */
        function getServerId() {
            if (window.localStorage) {
                if (localStorage.getItem("serverId")) {
                    //
                }
                else {
                    localStorage.setItem("serverId", guid());
                }
                return localStorage.getItem("serverId");
            }
            else {
                return guid();
            }
        }

        function startWps(req, t, callback) {
            function startWpsInnder(reqInner, tryCount, bPop) {
                if (tryCount < 1) {
                    if (callback)
                        callback({
                            status: 2,
                            message: "请允许浏览器打开WPS Office"
                        });
                    return;
                }
                var bRetry = true;
                var xmlReq = getHttpObj();
                //WPS客户端提供的接收参数的本地服务，HTTP服务端口为58890，HTTPS服务端口为58891
                //这俩配置，取一即可，不可同时启用
                xmlReq.open(reqInner.type, reqInner.url);
                xmlReq.onload = function (res) {
                    if (res.target.status != 200) {
                        var responseStr = IEVersion() < 10 ? xmlReq.responseText : res.target.response;
                        var errorMessage = JSON.parse(responseStr)
                        if (errorMessage.data == "Subserver not available." && tryCount == 4 && bPop) {
                            InitWpsCloudSvr();
                            setTimeout(function () {
                                if (bRetry) {
                                    bRetry = false;
                                    startWpsInnder(reqInner, --tryCount, false);
                                }
                            }, 3000);
                        }
                    }
                    if (callback)
                        callback({
                            status: 0,
                            res: res
                        });
                }
                xmlReq.ontimeout = xmlReq.onerror = function (res) {
                    xmlReq.bTimeout = true;
                    if (bPop) { //打开wps并传参
                        InitWpsCloudSvr()
                    }
                    setTimeout(function () {
                        if (bRetry) {
                            bRetry = false;
                            startWpsInnder(reqInner, --tryCount, false);
                        }
                    }, 1000);
                }
                if (IEVersion() < 10) {
                    xmlReq.onreadystatechange = function () {
                        if (xmlReq.readyState != 4)
                            return;
                        if (xmlReq.bTimeout) {
                            return;
                        }
                        if (xmlReq.status === 200)
                            xmlReq.onload();
                        else
                            xmlReq.onerror();
                    }
                }
                xmlReq.timeout = 3000;
                xmlReq.send(t)
            }
            startWpsInnder(req, 4, true);
            return;
        }

        function CheckPlugin(element) {
            var id = GetAddonId(element);
            var ele = document.getElementById(id + "_status");
            var xmlReq = getHttpObj();
            var offline = element.online === "false";
            var url = offline ? element.url : element.url + "ribbon.xml";
            xmlReq.open("POST", "http://127.0.0.1:58890/redirect/runParams");
            xmlReq.onload = function (res) {
                if ((offline && res.target.response.startsWith("7z"))
                    || !offline && res.target.response.startsWith("<customUI")) {
                    ele.style.color = "green";
                    ele.style.textAlign = "center";
                    ele.innerHTML = "正常";
                } else {
                    ele.style.color = "white";
                    ele.style.backgroundColor = "gray";
                    ele.style.textAlign = "center";
                    ele.innerHTML = "无效";
                    ele.title = offline ? ("不是有效的7z格式" + url) : ("不是有效的ribbon.xml，" + url);
                }
            }
            xmlReq.onerror = function (res) {
                xmlReq.bTimeout = true;
                ele.style.color = "white";
                ele.style.backgroundColor = "gray";
                ele.style.textAlign = "center";
                ele.innerHTML = "无效";
                ele.title = "网页路径不可访问，如果是跨域问题，不影响使用：" + url;
            }
            xmlReq.ontimeout = function (res) {
                xmlReq.bTimeout = true;
                ele.style.color = "white";
                ele.style.backgroundColor = "gray";
                ele.style.textAlign = "center";
                ele.innerHTML = "异常";
                ele.title = "访问超时，" + url;
            }
            if (IEVersion() < 10) {
                xmlReq.onreadystatechange = function () {
                    if (xmlReq.readyState != 4)
                        return;
                    if (xmlReq.bTimeout) {
                        return;
                    }
                    if (xmlReq.status === 200)
                        xmlReq.onload();
                    else
                        xmlReq.onerror();
                }
            }
            xmlReq.timeout = 5000;
            var data = {
                method: "get",
                url: url,
                data: ""
            }
            var sendData = FormatSendData(data)
            xmlReq.send(sendData);
        }

        function GetAddonId(element) {
            return element.name + "/" + element.addonType;
        }

        function UpdateElement(element, cmd) {
            if (typeof element.name === 'undefined')
                return
            var id = GetAddonId(element);
            var addonList = document.getElementById("addonList");
            //var param = JSON.stringify(element).replace(/"/g, "\'");
            var buttonLabel = cmd === 'enable' ? "安装" : "卸载";
            var des = "文字";
            if (element.addonType == "et")
                des = "电子表格";
            else if (element.addonType == "wpp")
                des = "演示";
            var loadType = "在线";
            if (element.online == "false")
                loadType = "离线";
            var old = document.getElementById(id);
            if (old !== null) {
                var oldOnline = old.wpsaddon.online === "false";
                var newOnline = element.online === "false";
                if (cmd === 'disable'
                    && (oldOnline !== newOnline
                        || old.wpsaddon.url !== element.url
                        || (oldOnline && old.wpsaddon.version !== element.version))) {
                    buttonLabel = "更新/卸载";
                    cmd = "choose";
                }
                old.wpsaddoncmd = cmd;
                document.getElementById(id + '_button').innerHTML = buttonLabel;
                document.getElementById(id + '_domain').innerHTML = element.customDomain;
            } else {
                var ele = document.createElement("div");
                ele.className = "addonItem";
                ele.id = id;
                ele.wpsaddon = element;
                ele.wpsaddoncmd = cmd;
                ele.innerHTML =
                    '<div class="addonItemName1">' + element.name + '</div>\n' +
                    '<div class="addonItemName2">' + des + '</div>\n' +
                    '<div class="addonItemName3">' + loadType + '</div>\n' +
                    '<div class="addonItemName4">' + element.url + '</div>\n' +
                    '<div class="addonItemName1" id="' + id + '_domain' + '">' + element.customDomain + '</div>\n' +
                    '<div class="addonItemName5"><div class="addonItemButton" id="' + id + '_button' + '" onclick="WpsAddonHandle(\'' + id + '\')">' + buttonLabel + '</div></div>\n' +
                    '<div class="addonItemName6" id="' + id + '_status' + '">验证中...</div>\n';
                addonList.appendChild(ele);
                CheckPlugin(element);
            }

            // 更新科技感按钮
            createTechButton(id, element, cmd);
        }

        function WpsAddonHandle(id) {
            var ele = document.getElementById(id);
            var element = ele.wpsaddon;
            var cmd = ele.wpsaddoncmd;
            WpsAddonHandleEx(element, cmd)
        }

        function WpsAddonHandleEx(element, cmd) {
            // 保存原始命令，用于后续显示正确的提示信息
            var originalCmd = cmd;

            if (cmd === "choose") {
                if (confirm("点击确定将更新 WPS 加载项，或点击取消完成卸载")) {
                    cmd = "enable";
                    originalCmd = "update"; // 标记为更新操作
                } else {
                    cmd = "disable";
                }
            }
            var data = FormartData(element, cmd);
            var req = { url: "http://127.0.0.1:58890/deployaddons/runParams", type: "POST" };
            startWps(req, data, function (res) {
                if (res.status == 0) {
                    if (cmd === "disableall") {
                        window.location.reload();
                    } else {
                        if (res.res.target.response == "OK"
                            || (res.res.target.response == "" && res.res.target.status == 200)) {
                            var newCmd = 'disable';
                            var successMessage = "安装成功！";

                            if (cmd === 'disable') {
                                newCmd = 'enable';
                                successMessage = "卸载成功！";
                            } else if (cmd === 'enable') {
                                if (originalCmd === 'update') {
                                    successMessage = "更新成功！";
                                } else {
                                    successMessage = "安装成功！";
                                }
                            }

                            UpdateElement(element, newCmd);
                            alert(successMessage);
                        }
                        else {
                            alert("配置失败！");
                        }
                    }
                } else {
                    alert(res.message);
                }
            });
        }

        function FormartData(element, cmd) {
            var data = {
                "cmd": cmd, //"enable", 启用， "disable", 禁用, "disableall", 禁用所有
                "name": element.name,
                "url": element.url,
                "addonType": element.addonType,
                "online": element.online,
                "version": element.version,
                "customDomain": element.customDomain
            }
            return FormatSendData(data);
        }

        function FormatSendData(data) {
            var strData = JSON.stringify(data);
            if (IEVersion() < 10)
                eval("strData = '" + JSON.stringify(strData) + "';");

            if (serverVersion >= "1.0.2" && serverId != undefined) {
                var base64Data = encode(strData);
                return JSON.stringify({
                    serverId: serverId,
                    data: base64Data
                })
            }
            else {
                return encode(strData);
            }
        }

        function LoadLocalAddons() {
            var baseData
            if (serverVersion >= "1.0.2" && serverId != undefined)
                baseData = JSON.stringify({ serverId: serverId });
            var req = { url: "http://127.0.0.1:58890/publishlist", type: "POST" };
            startWps(req, baseData, function (res) {
                if (res.status == 0) {
                    var addonList = document.getElementById("addonList");
                    var curList = JSON.parse(res.res.target.response);
                    curList.forEach(function (element) {
                        if (element.enable === "false")
                            return;
                        UpdateElement(element, 'disable')
                    });
                } else {
                    alert(res.message);
                }
            });
        }

        function LoadPublishAddons() {
            var addonList = document.getElementById("addonList");
            var curList = [
                {"name":"xdecai","addonType":"wps","online":"true","multiUser":"true","url":"http://10.142.36.62:3000/"}
            ];
            curList.forEach(function (element) {
                var param = JSON.stringify(element).replace("\"", "\'");
                UpdateElement(element, 'enable')
            });
        }

        var serverVersion = "wait";
        function InitSdk() {
            var req = { url: "http://127.0.0.1:58890/version", type: "POST" };
            startWps(
                req,
                JSON.stringify({ serverId: serverId }),
                function (res) {
                    if (res.status !== 0) {
                        return;
                    }
                    if (serverVersion == "wait") {
                        serverVersion = res.res.target.response;
                        LoadPublishAddons();
                        LoadLocalAddons();
                    }
                },
            );
        }

        function LoadAddons() {
            var addonList = document.getElementById("addonList");
            addonList.style.maxWidth = 800 * window.devicePixelRatio + "px";
            var ClearAll = document.getElementById("ClearAll");
            ClearAll.style.maxWidth = 800 * window.devicePixelRatio + "px";
            InitSdk();
        }

        function ClearAll() {
            if (confirm('确定要禁用所有WPS加载项吗？')) {
                var element = {};
                WpsAddonHandleEx(element, 'disableall');
            }
        }

        // 创建图片按钮的函数
        function createTechButton(id, element, cmd) {
            var buttonContainer = document.getElementById('tech-button-container');

            // 清空现有内容
            buttonContainer.innerHTML = '';

            // 保存当前命令状态，但不在按钮上显示文字
            var buttonLabel = cmd === 'enable' ? "安装" : (cmd === 'disable' ? "卸载" : "更新/卸载");

            var techButton = document.createElement('div');
            techButton.className = 'tech-button';
            techButton.setAttribute('data-action', buttonLabel); // 将操作类型保存为属性
            techButton.title = buttonLabel; // 设置鼠标悬停提示
            techButton.onclick = function() {
                WpsAddonHandle(id);
            };

            buttonContainer.appendChild(techButton);
        }

        // README下载功能
        function downloadReadme() {
            var link = document.createElement('a');
            link.href = '/西部钻探AI-使用说明书.pdf';
            link.download = '西部钻探AI-使用说明书.pdf';
            link.target = '_blank';
            document.body.appendChild(link);
            link.click();
            setTimeout(function() {
                document.body.removeChild(link);
            }, 100);
        }

        // WPS下载功能
        function downloadWPS() {
            var link = document.createElement('a');
            link.href = '/WPS.exe';
            link.download = 'WPS.exe';
            document.body.appendChild(link);
            link.click();
            setTimeout(function() {
                document.body.removeChild(link);
            }, 100);
            alert('WPS Office正在下载中，请稍候...');
        }
    </script>
    </body>
</html>
