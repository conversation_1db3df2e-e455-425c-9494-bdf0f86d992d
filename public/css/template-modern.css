/* 模板管理现代化UI样式 */
:root {
    --primary-color: #4568dc;
    --primary-gradient: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%);
    --secondary-color: #6c757d;
    --success-color: #10b981;
    --info-color: #3b82f6;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --border-radius: 12px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --transition: all 0.3s ease;
    --font-family: "Microsoft YaHei", "微软雅黑", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: var(--font-family);
}

body {
    background-color: #f8fafc;
    color: #334155;
    padding: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.container {
    max-width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* 现代化标题栏 */
.header {
    background: var(--primary-gradient);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 10;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
}

.header h1::before {
    content: "📄";
    display: inline-block;
    margin-right: 8px;
    font-size: 22px;
}

/* 模板管理容器 */
.template-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow: hidden;
    background-color: #f8fafc;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(69, 104, 220, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(176, 106, 179, 0.05) 0%, transparent 50%);
}

/* 搜索和筛选区域 */
.search-filter-container {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    align-items: center;
}

.search-box {
    flex: 1;
    position: relative;
}

.search-box input {
    width: 100%;
    padding: 12px 15px 12px 40px;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
    background-color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(69, 104, 220, 0.1);
}

.search-box::before {
    content: "🔍";
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: #94a3b8;
}

.filter-box select {
    padding: 12px 15px;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    font-size: 14px;
    min-width: 150px;
    background-color: white;
    transition: var(--transition);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%2394a3b8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 15px center;
    padding-right: 40px;
}

.filter-box select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(69, 104, 220, 0.1);
}

.create-template-btn {
    padding: 12px 20px;
    min-width: 140px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(69, 104, 220, 0.25);
    transition: all 0.3s ease;
}

.create-template-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(69, 104, 220, 0.35);
}

.create-template-btn .btn-icon {
    font-size: 18px;
    margin-right: 8px;
}

/* 功能按钮 */
.button-group {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
}

.template-actions, .template-card-actions {
    justify-content: flex-end;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-icon {
    margin-right: 8px;
    font-size: 16px;
}

.btn-recommend {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: black;
}

.btn-create {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: black;
}

/* 已移除从选中内容创建按钮样式 */

/* 模板列表 */
.template-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    overflow-y: auto;
    padding-right: 5px;
    margin-bottom: 15px;
}

.template-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 20px;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    border: 1px solid #e2e8f0;
}

.template-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.template-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
}

.template-badge {
    font-size: 12px;
    padding: 3px 10px;
    border-radius: 20px;
    font-weight: 500;
}

.template-badge.official {
    background-color: #e3f2fd;
    color: #1976d2;
}

.template-badge.custom {
    background-color: #e8f5e9;
    color: #388e3c;
}

.template-category {
    font-size: 12px;
    color: #64748b;
    margin-bottom: 8px;
}

.template-description {
    font-size: 14px;
    color: #334155;
    margin-bottom: 15px;
    flex-grow: 1;
    line-height: 1.5;
}

.template-card-actions, .template-actions {
    display: flex;
    justify-content: space-between;
    gap: 8px;
}

.template-card-actions button, .template-actions button {
    flex: 1;
    padding: 8px 0;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: none;
}

.btn-apply {
    background-color: var(--primary-color);
    color: white;
}

.btn-apply:hover {
    background-color: #3a57c4;
}

.btn-preview {
    background-color: white;
    color: var(--primary-color);
    border: 1px solid var(--primary-color) !important;
}

.btn-preview:hover {
    background-color: rgba(69, 104, 220, 0.05);
}

.btn-edit {
    background-color: white;
    color: var(--info-color);
    border: 1px solid var(--info-color) !important;
}

.btn-edit:hover {
    background-color: rgba(59, 130, 246, 0.05);
}

.btn-delete {
    background-color: white;
    color: var(--danger-color);
    border: 1px solid var(--danger-color) !important;
}

.btn-delete:hover {
    background-color: rgba(239, 68, 68, 0.05);
}

.no-templates {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px 20px;
    color: #64748b;
    font-style: italic;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
}

.recommendation-title {
    grid-column: 1 / -1;
    margin-bottom: 15px;
    color: var(--primary-color);
    font-size: 16px;
    font-weight: 600;
}

/* 状态消息 */
.status-message {
    font-size: 13px;
    padding: 5px 0;
    color: #64748b;
    min-height: 22px;
    text-align: center;
    transition: var(--transition);
}

.status-message.status-error {
    color: var(--danger-color);
}

.status-message.status-success {
    color: var(--success-color);
}

/* 对话框样式 */
.dialog {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(3px);
}

.dialog-content {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 25px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    animation: dialog-fade-in 0.3s ease;
}

@keyframes dialog-fade-in {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e2e8f0;
}

.dialog-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
}

.dialog-close {
    background: none;
    border: none;
    font-size: 22px;
    color: #94a3b8;
    cursor: pointer;
    padding: 0;
    transition: var(--transition);
    line-height: 1;
}

.dialog-close:hover {
    color: var(--danger-color);
}

.dialog-body {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #334155;
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-family: inherit;
    font-size: 14px;
    transition: var(--transition);
    background-color: white;
}

.form-group textarea {
    min-height: 150px;
    resize: vertical;
    line-height: 1.5;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(69, 104, 220, 0.1);
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.dialog-footer button {
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    border: none;
    box-shadow: 0 4px 10px rgba(69, 104, 220, 0.2);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(69, 104, 220, 0.3);
}

.btn-secondary {
    background-color: white;
    color: var(--secondary-color);
    border: 1px solid var(--secondary-color);
}

.btn-secondary:hover {
    background-color: #f8f9fa;
}

.preview-content {
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    max-height: 300px;
    overflow-y: auto;
    line-height: 1.5;
}

/* 格式化工具样式 */
.format-toolbar {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
    padding: 12px;
    background-color: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.format-btn {
    width: 40px;
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.format-btn:hover {
    background-color: #f0f3f9;
    border-color: var(--primary-color);
}

.format-icon {
    font-size: 16px;
    color: #334155;
}

.format-editor {
    min-height: 200px;
    font-family: 'Courier New', monospace;
    line-height: 1.5;
    resize: vertical;
}

.format-tips {
    margin-top: 15px;
    padding: 12px;
    background-color: #fff8e1;
    border-radius: 8px;
    font-size: 13px;
    border: 1px solid #ffecb3;
}

.format-tips ul {
    margin: 5px 0 0 20px;
    padding: 0;
}

.format-tips li {
    margin-bottom: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-filter-container {
        flex-direction: column;
    }

    .button-group {
        flex-wrap: wrap;
    }

    .action-btn {
        flex: 1;
        min-width: calc(33.33% - 10px);
    }

    .template-list {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }
}

@media (max-width: 480px) {
    .template-container {
        padding: 15px;
    }

    .header h1 {
        font-size: 18px;
    }

    .action-btn {
        min-width: calc(50% - 8px);
    }

    .template-list {
        grid-template-columns: 1fr;
    }

    .dialog-content {
        padding: 15px;
        width: 95%;
    }
}
