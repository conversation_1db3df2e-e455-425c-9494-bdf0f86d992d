/* 语言翻译现代化UI样式 */
:root {
    --primary-color: #4568dc;
    --primary-gradient: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%);
    --secondary-color: #6c757d;
    --success-color: #10b981;
    --info-color: #3b82f6;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --border-radius: 12px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --transition: all 0.3s ease;
    --font-family: "Microsoft YaHei", "微软雅黑", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: var(--font-family);
}

body {
    background-color: #f8fafc;
    color: #334155;
    padding: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.container {
    max-width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* 现代化标题栏 */
.header {
    background: var(--primary-gradient);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 10;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
}

.header h1::before {
    content: "";
    display: inline-block;
    width: 24px;
    height: 24px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    margin-right: 8px;
}

/* 状态指示器 */
.header-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

#serverStatus {
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.15);
    padding: 6px 12px;
    border-radius: 20px;
    backdrop-filter: blur(5px);
}

#statusIndicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    transition: var(--transition);
}

.status-checking {
    background-color: var(--warning-color);
}

.status-connected {
    background-color: var(--success-color);
}

.status-error {
    background-color: var(--danger-color);
}

#statusText {
    font-size: 12px;
    font-weight: 500;
    color: white;
}

#testConnectionBtn {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    margin-left: 10px;
    transition: var(--transition);
    backdrop-filter: blur(5px);
}

#testConnectionBtn:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* 翻译容器 */
.translation-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow: hidden;
    background-color: #f8fafc;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(69, 104, 220, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(176, 106, 179, 0.05) 0%, transparent 50%);
}

/* 输入区域 */
.input-section {
    margin-bottom: 20px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.section-header h2 {
    font-size: 16px;
    font-weight: 600;
    color: #334155;
}

.char-count {
    font-size: 12px;
    color: #64748b;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 4px 10px;
    border-radius: 12px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.text-area {
    width: 100%;
    height: 150px;
    padding: 15px;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    resize: none;
    outline: none;
    font-size: 14px;
    line-height: 1.6;
    transition: var(--transition);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
    background-color: white;
}

.text-area:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(69, 104, 220, 0.1);
}

/* 语言选择按钮 */
.language-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.lang-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border: none;
    border-radius: var(--border-radius);
    background-color: white;
    color: #334155;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    flex: 1;
    min-width: 80px;
}

.lang-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.lang-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-icon {
    font-size: 20px;
    margin-bottom: 5px;
}

.btn-text {
    font-size: 14px;
    font-weight: 500;
}

.zh-to-en {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: black;
}

.en-to-zh {
    background: linear-gradient(135deg, #ff0844 0%, #ffb199 100%);
    color: black;
}

.custom-lang {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: black;
}

.clear-btn {
    background: linear-gradient(135deg, #a1c4fd 0%, #c2e9fb 100%);
    color: #334155;
}

/* 输出区域 */
.output-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.action-btn {
    display: flex;
    align-items: center;
    padding: 6px 12px;
    border: none;
    border-radius: 20px;
    background-color: white;
    color: #334155;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    font-size: 12px;
}

.action-btn:hover {
    background-color: #f1f5f9;
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.action-btn .btn-icon {
    font-size: 14px;
    margin-right: 5px;
    margin-bottom: 0;
}

/* 结果区域 */
.result-box {
    flex: 1;
    padding: 15px;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    background-color: white;
    overflow-y: auto;
    font-size: 14px;
    line-height: 1.6;
    white-space: pre-wrap;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
    margin-top: 10px;
}

.result-box.success {
    border-color: var(--success-color);
    box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.1);
}

.result-box.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}



/* 加载指示器 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.loading-dots {
    display: flex;
    gap: 6px;
}

.loading-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-gradient);
    animation: dot-pulse 1.5s infinite ease-in-out;
}

.loading-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes dot-pulse {
    0%, 100% { transform: scale(0.8); opacity: 0.6; }
    50% { transform: scale(1.2); opacity: 1; }
}

/* 进度条 */
.progress-container {
    height: 4px;
    background-color: #e2e8f0;
    border-radius: 2px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-bar {
    height: 100%;
    background: var(--primary-gradient);
    width: 0%;
    transition: width 0.3s ease;
}

/* 状态消息 */
.status-message {
    font-size: 12px;
    padding: 5px 0;
    color: #64748b;
    min-height: 22px;
}

.status-message.status-error {
    color: var(--danger-color);
}

.status-message.status-success {
    color: var(--success-color);
}

/* 自定义语言对话框 */
.custom-lang-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.custom-lang-content {
    background-color: white;
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    width: 90%;
    max-width: 500px;
}

.custom-lang-content h3 {
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.3rem;
}

.lang-select-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 25px;
}

.lang-select {
    flex: 1;
}

.lang-arrow {
    margin: 0 15px;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.lang-select label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
}

.lang-select select {
    width: 100%;
    padding: 10px;
    border: 1px solid #e2e8f0;
    border-radius: 5px;
    font-family: inherit;
    font-size: 14px;
    background-color: white;
    transition: var(--transition);
}

.lang-select select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(69, 104, 220, 0.1);
}

.dialog-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.dialog-buttons button {
    min-width: 80px;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
    font-weight: 500;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
}

.btn-primary:hover {
    background-color: #3a57c4;
}

.btn-secondary {
    background-color: white;
    color: var(--secondary-color);
    border: 1px solid var(--secondary-color);
}

.btn-secondary:hover {
    background-color: #f8f9fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .language-buttons {
        flex-wrap: wrap;
    }

    .lang-btn {
        min-width: calc(50% - 5px);
    }
}

@media (max-width: 480px) {
    .translation-container {
        padding: 15px;
    }

    .header h1 {
        font-size: 18px;
    }

    .lang-select-container {
        flex-direction: column;
        gap: 15px;
    }

    .lang-arrow {
        transform: rotate(90deg);
        margin: 5px 0;
    }
}
