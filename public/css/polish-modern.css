/* 文本润色现代化UI样式 */
:root {
    --primary-color: #4568dc;
    --primary-gradient: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%);
    --secondary-color: #6c757d;
    --success-color: #10b981;
    --info-color: #3b82f6;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --border-radius: 12px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --transition: all 0.3s ease;
    --font-family: "Microsoft YaHei", "微软雅黑", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: var(--font-family);
}

body {
    background-color: #f8fafc;
    color: #334155;
    padding: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.container {
    max-width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* 现代化标题栏 */
.header {
    background: var(--primary-gradient);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 10;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
}

.header h1::before {
    content: "";
    display: inline-block;
    width: 24px;
    height: 24px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    margin-right: 8px;
}

/* 状态指示器 */
.header-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

#serverStatus {
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.15);
    padding: 6px 12px;
    border-radius: 20px;
    backdrop-filter: blur(5px);
}

#statusIndicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    transition: var(--transition);
}

.status-checking {
    background-color: var(--warning-color);
}

.status-connected {
    background-color: var(--success-color);
}

.status-error {
    background-color: var(--danger-color);
}

#statusText {
    font-size: 12px;
    font-weight: 500;
    color: white;
}

#testConnectionBtn {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    margin-left: 10px;
    transition: var(--transition);
    backdrop-filter: blur(5px);
}

#testConnectionBtn:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* 润色容器 */
.polish-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow: hidden;
    background-color: #f8fafc;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(69, 104, 220, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(176, 106, 179, 0.05) 0%, transparent 50%);
}

/* 输入区域 */
.input-section {
    margin-bottom: 20px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.section-header h2 {
    font-size: 16px;
    font-weight: 600;
    color: #334155;
}

.char-count {
    font-size: 12px;
    color: #64748b;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 4px 10px;
    border-radius: 12px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.text-area {
    width: 100%;
    height: 150px;
    padding: 15px;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    resize: none;
    outline: none;
    font-size: 14px;
    line-height: 1.6;
    transition: var(--transition);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
    background-color: white;
}

.text-area:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(69, 104, 220, 0.1);
}

/* 功能按钮区域 */
.function-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.function-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border: none;
    border-radius: var(--border-radius);
    background-color: white;
    color: #334155;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    flex: 1;
    min-width: 80px;
}

.function-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.function-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-icon {
    font-size: 20px;
    margin-bottom: 5px;
}

.btn-text {
    font-size: 14px;
    font-weight: 500;
}

.polish-btn {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: black;
}

.political-btn {
    background: linear-gradient(135deg, #ff0844 0%, #ffb199 100%);
    color: black;
}

.condense-btn {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: black;
}

.expand-btn {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: black;
}

.clear-btn {
    background: linear-gradient(135deg, #a1c4fd 0%, #c2e9fb 100%);
    color: #334155;
}

/* 输出区域 */
.output-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.action-btn {
    display: flex;
    align-items: center;
    padding: 6px 12px;
    border: none;
    border-radius: 20px;
    background-color: white;
    color: #334155;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    font-size: 12px;
}

.action-btn:hover {
    background-color: #f1f5f9;
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.action-btn .btn-icon {
    font-size: 14px;
    margin-right: 5px;
    margin-bottom: 0;
}

/* 结果区域 */
.result-box {
    flex: 1;
    padding: 15px;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    background-color: white;
    overflow-y: auto;
    font-size: 14px;
    line-height: 1.6;
    white-space: pre-wrap;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
    margin-top: 10px;
}

.result-box.success {
    border-color: var(--success-color);
    box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.1);
}

.result-box.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

/* 加载指示器 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.loading-dots {
    display: flex;
    gap: 6px;
}

.loading-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-gradient);
    animation: dot-pulse 1.5s infinite ease-in-out;
}

.loading-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes dot-pulse {
    0%, 100% { transform: scale(0.8); opacity: 0.6; }
    50% { transform: scale(1.2); opacity: 1; }
}

/* 进度条 */
.progress-container {
    height: 4px;
    background-color: #e2e8f0;
    border-radius: 2px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-bar {
    height: 100%;
    background: var(--primary-gradient);
    width: 0%;
    transition: width 0.3s ease;
}

/* 状态消息 */
.status-message {
    font-size: 12px;
    padding: 5px 0;
    color: #64748b;
    min-height: 22px;
}

.status-message.status-error {
    color: var(--danger-color);
}

.status-message.status-success {
    color: var(--success-color);
}

/* 思考过程框样式 */
.thinking-process {
    margin-top: 10px;
    margin-bottom: 10px;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    background-color: #f8fafc;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.thinking-header {
    padding: 8px 15px;
    background: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%);
    color: white;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.thinking-content {
    padding: 15px;
    font-size: 14px;
    line-height: 1.6;
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
    color: #334155;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .function-buttons {
        flex-wrap: wrap;
    }

    .function-btn {
        min-width: calc(33.33% - 10px);
    }
}

@media (max-width: 480px) {
    .function-btn {
        min-width: calc(50% - 10px);
    }

    .header h1 {
        font-size: 18px;
    }

    .polish-container {
        padding: 15px;
    }
}
