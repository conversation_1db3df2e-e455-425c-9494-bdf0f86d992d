/* 智能助手现代化UI样式 */
:root {
    --primary-color: #4568dc;
    --primary-gradient: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%);
    --secondary-color: #6c757d;
    --success-color: #10b981;
    --info-color: #3b82f6;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --border-radius: 12px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --transition: all 0.3s ease;
    --font-family: "Microsoft YaHei", "微软雅黑", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: var(--font-family);
}

body {
    background-color: #f8fafc;
    color: #334155;
    padding: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.container {
    max-width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* 现代化标题栏 */
.header {
    background: var(--primary-gradient);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 10;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
}

.header h1::before {
    content: "";
    display: inline-block;
    width: 24px;
    height: 24px;
    background-image: url('../images/assistant-icon.svg');
    background-size: contain;
    background-repeat: no-repeat;
    margin-right: 8px;
}

/* 状态指示器 */
.header-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

#serverStatus {
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.15);
    padding: 6px 12px;
    border-radius: 20px;
    backdrop-filter: blur(5px);
}

#statusIndicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    transition: var(--transition);
}

#statusText {
    font-size: 12px;
    font-weight: 500;
    color: white;
}

#testConnectionBtn {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    margin-left: 10px;
    transition: var(--transition);
    backdrop-filter: blur(5px);
}

#testConnectionBtn:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* 聊天容器 */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow: hidden;
    background-color: #f8fafc;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(69, 104, 220, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(176, 106, 179, 0.05) 0%, transparent 50%);
}

/* 聊天消息区域 */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    scroll-behavior: smooth;
}

/* 消息样式 */
.message {
    max-width: 85%;
    padding: 2px;
    position: relative;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.user-message {
    align-self: flex-end;
    margin-left: auto;
}

.assistant-message {
    align-self: flex-start;
    margin-right: auto;
}

/* 用户消息气泡 */
.user-message p {
    background: var(--primary-gradient);
    color: white;
    border-radius: 18px 18px 0 18px;
    padding: 12px 16px;
    box-shadow: 0 2px 10px rgba(69, 104, 220, 0.2);
}

/* 助手消息气泡 */
.assistant-message .final-answer {
    background-color: white;
    color: #334155;
    border-radius: 18px 18px 18px 0;
    padding: 14px 18px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-left: none;
    max-width: 100%;
}

/* 移除最终答案标题 */
.final-answer::before {
    display: none;
}

/* 思考过程样式 */
.thinking-process {
    color: #64748b;
    font-style: italic;
    margin-bottom: 15px;
    padding: 14px 18px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 18px 18px 18px 0;
    border-left: none;
    font-size: 0.9em;
    line-height: 1.6;
    overflow-x: auto;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    max-width: 100%;
    position: relative;
    backdrop-filter: blur(5px);
}

/* 思考过程标题 */
.thinking-process::before {
    content: '思考中...';
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--primary-color);
    font-style: normal;
    font-size: 0.9em;
}

/* 消息时间戳 */
.message-time {
    font-size: 11px;
    color: #94a3b8;
    margin-top: 5px;
    text-align: right;
}

/* 加载指示器 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.loading-dots {
    display: flex;
    gap: 6px;
}

.loading-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--primary-color);
    animation: pulse 1.5s infinite ease-in-out;
}

.loading-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes pulse {
    0%, 100% { transform: scale(0.8); opacity: 0.6; }
    50% { transform: scale(1.2); opacity: 1; }
}

/* 进度条容器 */
.progress-container {
    height: 4px;
    background-color: rgba(0, 0, 0, 0.05);
    margin: 0;
    overflow: hidden;
    border-radius: 2px;
}

.progress-bar {
    height: 100%;
    width: 0;
    background: var(--primary-gradient);
    transition: width 0.3s ease;
}

/* 输入容器 */
.input-container {
    display: flex;
    gap: 10px;
    padding: 15px 20px;
    background-color: white;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 5;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
}

.message-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #e2e8f0;
    border-radius: 24px;
    resize: none;
    outline: none;
    font-size: 14px;
    line-height: 1.5;
    transition: var(--transition);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
    max-height: 150px;
}

.message-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(69, 104, 220, 0.1);
}

.send-button {
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 2px 10px rgba(69, 104, 220, 0.2);
    flex-shrink: 0;
}

.send-button::before {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M2.01 21L23 12 2.01 3 2 10l15 2-15 2z'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
}

.send-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(69, 104, 220, 0.3);
}

.send-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 代码块样式 */
.assistant-message pre {
    background-color: #1e293b;
    border-radius: 8px;
    padding: 16px;
    margin: 12px 0;
    overflow-x: auto;
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-left: 4px solid var(--primary-color);
}

.assistant-message pre::before {
    content: "代码";
    position: absolute;
    top: 0;
    right: 0;
    background: var(--primary-gradient);
    color: white;
    padding: 2px 8px;
    font-size: 12px;
    border-radius: 0 8px 0 8px;
    font-weight: 500;
}

.assistant-message pre code {
    color: #e2e8f0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.6;
    display: block;
    white-space: pre;
}

/* 行内代码样式 */
.assistant-message code:not(pre code) {
    background-color: rgba(226, 232, 240, 0.3);
    border-radius: 4px;
    padding: 2px 6px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9em;
    color: var(--primary-color);
    white-space: nowrap;
}

/* 语言特定代码块样式 */
.assistant-message pre.language-javascript::before,
.assistant-message pre.language-js::before {
    content: "JavaScript";
}

.assistant-message pre.language-html::before {
    content: "HTML";
}

.assistant-message pre.language-css::before {
    content: "CSS";
}

.assistant-message pre.language-python::before {
    content: "Python";
}

.assistant-message pre.language-java::before {
    content: "Java";
}

.assistant-message pre.language-csharp::before,
.assistant-message pre.language-cs::before {
    content: "C#";
}

.assistant-message pre.language-cpp::before {
    content: "C++";
}

.assistant-message pre.language-c::before {
    content: "C";
}

.assistant-message pre.language-php::before {
    content: "PHP";
}

.assistant-message pre.language-ruby::before {
    content: "Ruby";
}

.assistant-message pre.language-go::before {
    content: "Go";
}

.assistant-message pre.language-rust::before {
    content: "Rust";
}

.assistant-message pre.language-swift::before {
    content: "Swift";
}

.assistant-message pre.language-kotlin::before {
    content: "Kotlin";
}

.assistant-message pre.language-typescript::before,
.assistant-message pre.language-ts::before {
    content: "TypeScript";
}

.assistant-message pre.language-bash::before,
.assistant-message pre.language-shell::before {
    content: "Shell";
}

.assistant-message pre.language-sql::before {
    content: "SQL";
}

.assistant-message pre.language-json::before {
    content: "JSON";
}

.assistant-message pre.language-xml::before {
    content: "XML";
}

.assistant-message pre.language-yaml::before,
.assistant-message pre.language-yml::before {
    content: "YAML";
}

/* 复制按钮 */
.code-copy-btn {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background-color: rgba(255, 255, 255, 0.1);
    color: #e2e8f0;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
    opacity: 0;
}

.assistant-message pre:hover .code-copy-btn {
    opacity: 1;
}

.code-copy-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* LaTeX公式样式 */
.latex-block {
    display: block;
    margin: 15px 0;
    padding: 10px;
    background-color: rgba(240, 245, 255, 0.5);
    border-radius: 8px;
    overflow-x: auto;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.latex-inline {
    display: inline-block;
    vertical-align: middle;
    margin: 0 2px;
}

/* MathJax SVG样式调整 */
.MathJax {
    outline: none;
}

.assistant-message .MathJax_SVG_Display {
    margin: 10px 0;
}

.assistant-message .MathJax_SVG {
    outline: none;
}
