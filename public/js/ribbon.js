
// 定义全局变量来存储原始选择的位置信息
let originalSelectionRange = null;
let originalSelectionText = "";

// 定义所有任务窗格的ID存储键
const TASKPANE_IDS = {
    polish: "taskpane_id",
    translation: "translation_taskpane_id",
    template: "template_taskpane_id",
    assistant: "assistant_taskpane_id",
    patent: "patent_taskpane_id"
};

// 关闭所有任务窗格的函数
function closeAllTaskPanes() {
    // 遍历所有任务窗格ID
    for (const key in TASKPANE_IDS) {
        const storageKey = TASKPANE_IDS[key];
        const paneId = window.Application.PluginStorage.getItem(storageKey);

        if (paneId) {
            try {
                const pane = window.Application.GetTaskPane(paneId);
                pane.Delete();
                console.log(`关闭${key}任务窗格成功`);
            } catch (e) {
                console.log(`关闭${key}任务窗格失败，可能已经关闭: ${e.message}`);
            }
            window.Application.PluginStorage.removeItem(storageKey);
        }
    }
}

//这个函数在整个wps加载项中是第一个执行的
function OnAddinLoad(ribbonUI){
    if (typeof (window.Application.ribbonUI) != "object"){
		window.Application.ribbonUI = ribbonUI
    }

    if (typeof (window.Application.Enum) != "object") { // 如果没有内置枚举值
        window.Application.Enum = WPS_Enum
    }

    return true
}

// 文本润色功能初始化 - 在taskpane加载时调用
function initTextPolishFeature() {
    // 页面加载时获取选中的文本
    const selectedText = getSelectedText();

    // 如果有选中的文本，填充到输入框
    if (selectedText) {
        setInputText(selectedText);
        updateCharCount(); // 更新字符计数
    } else {
        // 初始化字符计数
        updateCharCount();
    }

    // 确保结果区域和状态消息有正确的类名
    document.getElementById('polishedText').className = 'result-box';
    document.getElementById('statusMessage').className = 'status-message';

    // 确保复制和替换按钮初始化为禁用状态
    document.getElementById('copyBtn').disabled = true;
    document.getElementById('replaceBtn').disabled = true;

    // 获取所有润色风格按钮并确保它们可用
    const allStyleButtons = [
        document.getElementById('polishBtn'),
        document.getElementById('expandBtn'),
        document.getElementById('condenseBtn'),
        document.getElementById('politicalBtn')
    ];

    allStyleButtons.forEach(btn => {
        if (btn) btn.disabled = false;
    });

    // 设置文本域获取焦点事件
    const inputTextElement = document.getElementById('inputText');
    if (inputTextElement) {
        inputTextElement.addEventListener('focus', function() {
            if (this.value === '') {
                // 已经使用placeholder属性，不需要清除默认文本
            }
        });
    }

    // 检查是否有需要自动执行的操作
    checkAndExecutePendingAction();
}

// 全局变量，用于存储待执行的操作
let pendingAction = null;

// 设置待执行的操作
function setPendingAction(action, text) {
    pendingAction = {
        action: action,
        text: text
    };
}

// 检查并执行待执行的操作
function checkAndExecutePendingAction() {
    if (pendingAction) {
        const action = pendingAction.action;
        const text = pendingAction.text;

        // 设置输入文本
        document.getElementById('inputText').value = text;
        // 更新字符计数
        updateCharCount();

        // 根据不同的操作执行相应的功能
        switch(action) {
            case 'polish':
                polishText('polish');
                break;
            case 'expand':
                polishText('expand');
                break;
            case 'condense':
                polishText('condense');
                break;
            case 'political':
                polishText('political');
                break;
        }

        // 清除待执行的操作
        pendingAction = null;
    }
}

function OnAction(control) {
    const eleId = control.Id
    switch (eleId) {
        // 处理润色按钮
        case "btnPolish":
            {
                try {
                    // 使用getSelectedText函数获取选中的文本，该函数会进行严格检查
                    const selectedText = getSelectedText();
                    if (!selectedText) {
                        alert("请先选中需要润色的文本（必须明确选中文本，而不仅是将光标放在文本前）");
                        return true; // 确保完全退出函数
                    }

                    // getSelectedText函数已经保存了原始选择位置和文本
                    const originalText = selectedText;

                    // 将文本进行 URL 编码
                    const encodedText = encodeURIComponent(originalText);
                    // 创建带参数的URL - 使用新的现代化UI
                    const taskpaneUrl = GetUrlPath() + "/ui/polish-modern.html?action=polish&text=" + encodedText;

                    // 关闭所有现有的任务窗格
                    closeAllTaskPanes();

                    // 创建新的任务窗格，带参数
                    let tskpane = window.Application.CreateTaskPane(taskpaneUrl);
                    let id = tskpane.ID;
                    window.Application.PluginStorage.setItem("taskpane_id", id);
                    tskpane.Visible = true;
                } catch (error) {
                    console.error("润色功能错误:", error);
                    // 避免显示技术性错误给用户
                    if (!error.message.includes("请先选中")) {
                        alert("执行润色功能时出错，请重试");
                    }
                }
            }
            break;
        // 处理扩写按钮
        case "btnExpansion":
            {
                try {
                    // 使用getSelectedText函数获取选中的文本，该函数会进行严格检查
                    const selectedText = getSelectedText();
                    if (!selectedText) {
                        alert("请先选中需要扩写的文本（必须明确选中文本，而不仅是将光标放在文本前）");
                        return true; // 确保完全退出函数
                    }

                    // getSelectedText函数已经保存了原始选择位置和文本
                    const originalText = selectedText;

                    // 将文本进行 URL 编码
                    const encodedText = encodeURIComponent(originalText);
                    // 创建带参数的URL - 使用新的现代化UI
                    const taskpaneUrl = GetUrlPath() + "/ui/polish-modern.html?action=expand&text=" + encodedText;

                    // 关闭所有现有的任务窗格
                    closeAllTaskPanes();

                    // 创建新的任务窗格，带参数
                    let tskpane = window.Application.CreateTaskPane(taskpaneUrl);
                    let id = tskpane.ID;
                    window.Application.PluginStorage.setItem("taskpane_id", id);
                    tskpane.Visible = true;
                } catch (error) {
                    console.error("扩写功能错误:", error);
                    // 避免显示技术性错误给用户
                    if (!error.message.includes("请先选中")) {
                        alert("执行扩写功能时出错，请重试");
                    }
                }
            }
            break;
        // 处理缩写按钮
        case "btnCondense":
            {
                try {
                    // 使用getSelectedText函数获取选中的文本，该函数会进行严格检查
                    const selectedText = getSelectedText();
                    if (!selectedText) {
                        alert("请先选中需要缩写的文本（必须明确选中文本，而不仅是将光标放在文本前）");
                        return true; // 确保完全退出函数
                    }

                    // getSelectedText函数已经保存了原始选择位置和文本
                    const originalText = selectedText;

                    // 将文本进行 URL 编码
                    const encodedText = encodeURIComponent(originalText);
                    // 创建带参数的URL - 使用新的现代化UI
                    const taskpaneUrl = GetUrlPath() + "/ui/polish-modern.html?action=condense&text=" + encodedText;

                    // 关闭所有现有的任务窗格
                    closeAllTaskPanes();

                    // 创建新的任务窗格，带参数
                    let tskpane = window.Application.CreateTaskPane(taskpaneUrl);
                    let id = tskpane.ID;
                    window.Application.PluginStorage.setItem("taskpane_id", id);
                    tskpane.Visible = true;
                } catch (error) {
                    console.error("缩写功能错误:", error);
                    // 避免显示技术性错误给用户
                    if (!error.message.includes("请先选中")) {
                        alert("执行缩写功能时出错，请重试");
                    }
                }
            }
            break;
        // 处理党政风按钮
        case "btnPartyStyle":
            {
                try {
                    // 使用getSelectedText函数获取选中的文本，该函数会进行严格检查
                    const selectedText = getSelectedText();
                    if (!selectedText) {
                        alert("请先选中需要转换为党政风格的文本（必须明确选中文本，而不仅是将光标放在文本前）");
                        return true; // 确保完全退出函数
                    }

                    // getSelectedText函数已经保存了原始选择位置和文本
                    const originalText = selectedText;

                    // 将文本进行 URL 编码
                    const encodedText = encodeURIComponent(originalText);
                    // 创建带参数的URL - 使用新的现代化UI
                    const taskpaneUrl = GetUrlPath() + "/ui/polish-modern.html?action=political&text=" + encodedText;

                    // 关闭所有现有的任务窗格
                    closeAllTaskPanes();

                    // 创建新的任务窗格，带参数
                    let tskpane = window.Application.CreateTaskPane(taskpaneUrl);
                    let id = tskpane.ID;
                    window.Application.PluginStorage.setItem("taskpane_id", id);
                    tskpane.Visible = true;
                } catch (error) {
                    console.error("党政风功能错误:", error);
                    // 避免显示技术性错误给用户
                    if (!error.message.includes("请先选中")) {
                        alert("执行党政风功能时出错，请重试");
                    }
                }
            }
            break;

        case "btnShowTaskPane":
            {
                // 检查是否已有任务窗格
                let tsId = window.Application.PluginStorage.getItem("taskpane_id")
                if (!tsId) {
                    // 关闭所有现有的任务窗格
                    closeAllTaskPanes();

                    // 创建新的任务窗格 - 使用新的现代化UI
                    let tskpane = window.Application.CreateTaskPane(GetUrlPath() + "/ui/polish-modern.html")
                    let id = tskpane.ID
                    window.Application.PluginStorage.setItem("taskpane_id", id)
                    tskpane.Visible = true
                } else {
                    // 如果已有任务窗格，则切换其可见性
                    let tskpane = window.Application.GetTaskPane(tsId)
                    if (tskpane.Visible) {
                        // 如果当前可见，则隐藏
                        tskpane.Visible = false
                    } else {
                        // 如果当前隐藏，则先关闭其他面板，再显示此面板
                        closeAllTaskPanes();
                        // 重新获取任务窗格（因为可能已被关闭）
                        tsId = window.Application.PluginStorage.getItem("taskpane_id")
                        if (tsId) {
                            try {
                                tskpane = window.Application.GetTaskPane(tsId)
                                tskpane.Visible = true
                            } catch (e) {
                                console.log("获取任务窗格失败，创建新窗格");
                                // 使用新的现代化UI
                                let newPane = window.Application.CreateTaskPane(GetUrlPath() + "/ui/polish-modern.html")
                                let id = newPane.ID
                                window.Application.PluginStorage.setItem("taskpane_id", id)
                                newPane.Visible = true
                            }
                        } else {
                            // 如果ID已被清除，创建新窗格 - 使用新的现代化UI
                            let newPane = window.Application.CreateTaskPane(GetUrlPath() + "/ui/polish-modern.html")
                            let id = newPane.ID
                            window.Application.PluginStorage.setItem("taskpane_id", id)
                            newPane.Visible = true
                        }
                    }
                }
            }
            break
        case "btnTranslation":
            {
                // 检查是否已有翻译任务窗格
                let translationId = window.Application.PluginStorage.getItem("translation_taskpane_id")
                if (!translationId) {
                    // 关闭所有现有的任务窗格
                    closeAllTaskPanes();

                    // 创建新的翻译任务窗格
                    let tskpane = window.Application.CreateTaskPane(GetUrlPath() + "/ui/translation.html")
                    let id = tskpane.ID
                    window.Application.PluginStorage.setItem("translation_taskpane_id", id)
                    tskpane.Visible = true
                } else {
                    // 如果已有翻译任务窗格，则切换其可见性
                    let tskpane = window.Application.GetTaskPane(translationId)
                    if (tskpane.Visible) {
                        // 如果当前可见，则隐藏
                        tskpane.Visible = false
                    } else {
                        // 如果当前隐藏，则先关闭其他面板，再显示此面板
                        closeAllTaskPanes();
                        // 重新获取任务窗格（因为可能已被关闭）
                        translationId = window.Application.PluginStorage.getItem("translation_taskpane_id")
                        if (translationId) {
                            try {
                                tskpane = window.Application.GetTaskPane(translationId)
                                tskpane.Visible = true
                            } catch (e) {
                                console.log("获取翻译任务窗格失败，创建新窗格");
                                let newPane = window.Application.CreateTaskPane(GetUrlPath() + "/ui/translation.html")
                                let id = newPane.ID
                                window.Application.PluginStorage.setItem("translation_taskpane_id", id)
                                newPane.Visible = true
                            }
                        } else {
                            // 如果ID已被清除，创建新窗格
                            let newPane = window.Application.CreateTaskPane(GetUrlPath() + "/ui/translation.html")
                            let id = newPane.ID
                            window.Application.PluginStorage.setItem("translation_taskpane_id", id)
                            newPane.Visible = true
                        }
                    }
                }
            }
            break
        case "btnTemplate":
            {
                // 检查是否已有模板管理任务窗格
                let templateId = window.Application.PluginStorage.getItem("template_taskpane_id")
                if (!templateId) {
                    // 关闭所有现有的任务窗格
                    closeAllTaskPanes();

                    // 创建新的模板管理任务窗格
                    let tskpane = window.Application.CreateTaskPane(GetUrlPath() + "/ui/template.html")
                    let id = tskpane.ID
                    window.Application.PluginStorage.setItem("template_taskpane_id", id)
                    tskpane.Visible = true
                } else {
                    // 如果已有模板管理任务窗格，则切换其可见性
                    let tskpane = window.Application.GetTaskPane(templateId)
                    if (tskpane.Visible) {
                        // 如果当前可见，则隐藏
                        tskpane.Visible = false
                    } else {
                        // 如果当前隐藏，则先关闭其他面板，再显示此面板
                        closeAllTaskPanes();
                        // 重新获取任务窗格（因为可能已被关闭）
                        templateId = window.Application.PluginStorage.getItem("template_taskpane_id")
                        if (templateId) {
                            try {
                                tskpane = window.Application.GetTaskPane(templateId)
                                tskpane.Visible = true
                            } catch (e) {
                                console.log("获取模板管理任务窗格失败，创建新窗格");
                                let newPane = window.Application.CreateTaskPane(GetUrlPath() + "/ui/template.html")
                                let id = newPane.ID
                                window.Application.PluginStorage.setItem("template_taskpane_id", id)
                                newPane.Visible = true
                            }
                        } else {
                            // 如果ID已被清除，创建新窗格
                            let newPane = window.Application.CreateTaskPane(GetUrlPath() + "/ui/template.html")
                            let id = newPane.ID
                            window.Application.PluginStorage.setItem("template_taskpane_id", id)
                            newPane.Visible = true
                        }
                    }
                }
            }
            break

        // 处理中译英按钮
        case "btnZhToEn":
            {
                try {
                    // 使用getSelectedText函数获取选中的文本，该函数会进行严格检查
                    const selectedText = getSelectedText();
                    if (!selectedText) {
                        alert("请先选中需要翻译的文本（必须明确选中文本，而不仅是将光标放在文本前）");
                        return true; // 确保完全退出函数
                    }

                    // 检查文本长度是否超过2个字符
                    if (selectedText.length <= 2) {
                        alert("选中的文本太短，请选择至少三个字符");
                        return true; // 确保完全退出函数
                    }

                    const trimmedText = selectedText;

                    // 将文本进行 URL 编码
                    const encodedText = encodeURIComponent(trimmedText);
                    // 创建带参数的URL
                    const taskpaneUrl = GetUrlPath() + "/ui/translation.html?action=translate&sourceLang=zh&targetLang=en&text=" + encodedText;

                    // 关闭所有现有的任务窗格
                    closeAllTaskPanes();

                    // 创建新的任务窗格，带参数
                    let tskpane = window.Application.CreateTaskPane(taskpaneUrl);
                    let id = tskpane.ID;
                    window.Application.PluginStorage.setItem("translation_taskpane_id", id);
                    tskpane.Visible = true;
                } catch (error) {
                    console.error("中译英功能错误:", error);
                    // 避免显示技术性错误给用户
                    if (!error.message.includes("请先选中") && !error.message.includes("太短")) {
                        alert("执行中译英功能时出错，请重试");
                    }
                }
            }
            break
        // 处理英译中按钮
        case "btnEnToZh":
            {
                try {
                    // 使用getSelectedText函数获取选中的文本，该函数会进行严格检查
                    const selectedText = getSelectedText();
                    if (!selectedText) {
                        alert("请先选中需要翻译的文本（必须明确选中文本，而不仅是将光标放在文本前）");
                        return true; // 确保完全退出函数
                    }

                    // 检查文本长度是否超过2个字符
                    if (selectedText.length <= 2) {
                        alert("选中的文本太短，请选择至少三个字符");
                        return true; // 确保完全退出函数
                    }

                    const trimmedText = selectedText;

                    // 将文本进行 URL 编码
                    const encodedText = encodeURIComponent(trimmedText);
                    // 创建带参数的URL
                    const taskpaneUrl = GetUrlPath() + "/ui/translation.html?action=translate&sourceLang=en&targetLang=zh&text=" + encodedText;

                    // 关闭所有现有的任务窗格
                    closeAllTaskPanes();

                    // 创建新的任务窗格，带参数
                    let tskpane = window.Application.CreateTaskPane(taskpaneUrl);
                    let id = tskpane.ID;
                    window.Application.PluginStorage.setItem("translation_taskpane_id", id);
                    tskpane.Visible = true;
                } catch (error) {
                    console.error("英译中功能错误:", error);
                    // 避免显示技术性错误给用户
                    if (!error.message.includes("请先选中") && !error.message.includes("太短")) {
                        alert("执行英译中功能时出错，请重试");
                    }
                }
            }
            break
        // 处理自定义语言按钮
        case "btnCustomLang":
            {
                try {
                    // 使用getSelectedText函数获取选中的文本，该函数会进行严格检查
                    const selectedText = getSelectedText();
                    if (!selectedText) {
                        alert("请先选中需要翻译的文本（必须明确选中文本，而不仅是将光标放在文本前）");
                        return true; // 确保完全退出函数
                    }

                    // 检查文本长度是否超过2个字符
                    if (selectedText.length <= 2) {
                        alert("选中的文本太短，请选择至少三个字符");
                        return true; // 确保完全退出函数
                    }

                    const trimmedText = selectedText;

                    // 将文本进行 URL 编码
                    const encodedText = encodeURIComponent(trimmedText);
                    // 创建带参数的URL
                    const taskpaneUrl = GetUrlPath() + "/ui/translation.html?action=customLang&text=" + encodedText;

                    // 关闭所有现有的任务窗格
                    closeAllTaskPanes();

                    // 创建新的任务窗格，带参数
                    let tskpane = window.Application.CreateTaskPane(taskpaneUrl);
                    let id = tskpane.ID;
                    window.Application.PluginStorage.setItem("translation_taskpane_id", id);
                    tskpane.Visible = true;
                } catch (error) {
                    console.error("自定义翻译功能错误:", error);
                    // 避免显示技术性错误给用户
                    if (!error.message.includes("请先选中") && !error.message.includes("太短")) {
                        alert("执行自定义翻译功能时出错，请重试");
                    }
                }
            }
            break
        case "btnSmartAssistant":
            {
                // 智能助手不需要自动获取选中的文本
                // 直接使用基本 URL，不传递选中文本
                let taskpaneUrl = GetUrlPath() + "/ui/assistant.html";

                // 关闭所有现有的任务窗格
                closeAllTaskPanes();

                // 创建新的任务窗格
                let tskpane = window.Application.CreateTaskPane(taskpaneUrl);
                let id = tskpane.ID;
                window.Application.PluginStorage.setItem("assistant_taskpane_id", id);
                tskpane.Visible = true;
            }
            break
        case "btnXdecai":
            {
                try {
                    // 使用ShowDialog方法打开自定义对话框，设置无边框模式
                    window.Application.ShowDialog(
                        GetUrlPath() + "/ui/about.html",  // 使用自定义HTML页面
                        "",  // 空标题，不显示标题栏
                        400 * window.devicePixelRatio,
                        450 * window.devicePixelRatio,
                        false,  // 非模态对话框
                        true    // 无边框模式
                    );
                } catch (e) {
                    console.error('打开关于对话框失败:', e);

                    // 如果打开对话框失败，回退到使用Alert方法
                    const aboutInfo = "西部钻探软件研发中心\n\n"
                        + "本插件集成了文本润色、语言翻译、"
                        + "文档模板管理和智能助手等功能，方便用户高效完成文档处理工作。\n\n"
                        + "版本：1.0.0\n"
                        + "发布日期：2025年4月";

                    try {
                        window.Application.Alert(aboutInfo, "关于西部钻探软件研发中心");
                    } catch (alertError) {
                        // 如果 Alert 方法不可用，尝试使用浏览器的 alert
                        alert(aboutInfo);
                    }
                }
            }
            break
        case "btnPatent":
            {
                // 不再从选中文本获取项目名称，直接打开专利交底书页面
                let taskpaneUrl = GetUrlPath() + "/ui/patent.html";



                // 关闭所有现有的任务窗格
                closeAllTaskPanes();

                // 创建新的任务窗格
                let tskpane = window.Application.CreateTaskPane(taskpaneUrl);
                let id = tskpane.ID;
                window.Application.PluginStorage.setItem("patent_taskpane_id", id);
                tskpane.Visible = true;
            }
            break
        case "btnCompanyLogo":
            {
                // 装饰性按钮，不执行任何操作
                // 这样按钮可以显示彩色图标但点击时不会有任何反应
                console.log("装饰性按钮被点击");
            }
            break
        default:
            break
    }
    return true
}

function GetImage(control) {
    const eleId = control.Id
    switch (eleId) {
        case "btnShowTaskPane":
            return "images/wenben.png"  // 更换为文本润色更合适的图标
        case "btnTranslation":
            return "images/translation.png"  // 为翻译按钮使用新图标
        case "btnTemplate":
            return "images/moban.png"  // 为模板管理按钮使用模板图标
        case "btnPolish":
            return "images/runse.png"  // 润色按钮图标
        case "btnExpansion":
            return "images/kuoxie.png"  // 扩写按钮图标
        case "btnCondense":
            return "images/suoxie.png"  // 缩写按钮图标
        case "btnPartyStyle":
            return "images/dangzheng.png"  // 党政风按钮图标
        case "btnZhToEn":
            return "images/zh-to-en.png"  // 中译英按钮图标
        case "btnEnToZh":
            return "images/en-to-zh.png"  // 英译中按钮图标
        case "btnCustomLang":
            return "images/custom-lang.png"  // 自定义语言按钮图标
        case "btnSmartAssistant":
            return "images/assistant.png"  // 智能助手按钮图标
        case "btnPatent":
            return "images/patent.png"  // 专利申请技术交底书按钮图标
        case "btnXdecai":
            return "images/xdecai.png"  // 西部钻探软件研发中心按钮图标
        case "btnCompanyLogo":
            return "images/logo.png"  // 西部钻探工程有限公司装饰性按钮图标
        default:
            ;
    }
    return "images/newFromTemp.svg"
}

function OnGetEnabled() {
    // 所有按钮默认启用
    return true
}

function OnGetVisible(){
    // 所有按钮默认可见
    return true
}

function OnGetLabel(){
    // 默认不修改按钮标签
    return ""
}

// ===== 文本润色功能相关函数 =====

// 打字机效果函数
function typeWriter(element, text, speed = 20) {
    let i = 0;
    element.innerHTML = '';

    // 先禁用复制和替换按钮，等打字机效果完成后再启用
    document.getElementById('copyBtn').disabled = true;
    document.getElementById('replaceBtn').disabled = true;

    function typing() {
        if (i < text.length) {
            // 处理换行符
            if (text.charAt(i) === '\n') {
                element.innerHTML += '<br>';
            }
            // 处理中文空格（缩进）
            else if (text.charAt(i) === '　') {
                element.innerHTML += '&emsp;';
            }
            else {
                element.innerHTML += text.charAt(i);
            }
            i++;
            setTimeout(typing, speed);
        } else {
            // 打字机效果完成后启用复制和替换按钮
            document.getElementById('copyBtn').disabled = false;
            document.getElementById('replaceBtn').disabled = false;
        }
    }

    typing();
}

// 润色文本的主函数
async function polishText(style = 'polish') {
    // 先获取当前选中的文本（这个函数会保存原始选择位置）
    const selectedText = getSelectedText();
    if (selectedText) {
        // 如果有选中的文本，则更新输入框
        setInputText(selectedText);
        updateCharCount();
    } else {
        // 如果没有新选中的文本，但输入框中有内容，且与上次选中的文本不同
        // 则清除原始选择位置，因为用户可能手动修改了输入框
        const inputText = document.getElementById('inputText').value.trim();
        if (inputText && inputText !== originalSelectionText) {
            console.log('输入框内容已更改，清除原始选择位置');
            originalSelectionRange = null;
            originalSelectionText = "";
        }
    }

    // 获取输入框中的文本（可能是刚刚更新的选中文本）
    const inputText = document.getElementById('inputText').value.trim();
    const outputDiv = document.getElementById('polishedText');
    const loadingDiv = document.getElementById('loading');
    const copyBtn = document.getElementById('copyBtn');

    // 获取所有润色风格按钮
    const allStyleButtons = [
        document.getElementById('polishBtn'),
        document.getElementById('expandBtn'),
        document.getElementById('condenseBtn'),
        document.getElementById('politicalBtn')
    ];

    // 验证输入
    if (!inputText) {
        showError(outputDiv, '请输入要润色的文本');
        return;
    }

    // 准备请求
    outputDiv.innerHTML = '';
    outputDiv.className = 'result-box';
    outputDiv.style.display = 'none';

    // 获取并清空思考过程框
    const thinkingDiv = document.getElementById('thinkingProcess');
    const thinkingContent = thinkingDiv.querySelector('.thinking-content');
    thinkingContent.innerText = '';
    thinkingDiv.style.display = 'none';

    loadingDiv.style.display = 'block';

    // 禁用所有润色风格按钮
    allStyleButtons.forEach(btn => {
        if (btn) btn.disabled = true;
    });

    // 禁用复制和替换按钮
    copyBtn.disabled = true;
    document.getElementById('replaceBtn').disabled = true;

    try {
        // 调用GPUStack API进行文本润色，传入润色风格
        // 注意：callPolishAPI会在接收到数据时实时更新outputDiv
        const finalResult = await callPolishAPI(inputText, style);

        // 处理完成后
        loadingDiv.style.display = 'none';
        outputDiv.className = 'result-box success';

        // 检查结果是否为空
        if (!finalResult || finalResult.trim() === '') {
            console.error('润色结果为空');
            showError(outputDiv, '润色结果为空，请重试');

            // 重新启用所有按钮
            allStyleButtons.forEach(btn => {
                if (btn) btn.disabled = false;
            });
            return;
        }

        console.log('润色完成，最终结果长度:', finalResult.length);

        // 启用复制和替换按钮
        document.getElementById('copyBtn').disabled = false;
        document.getElementById('replaceBtn').disabled = false;

        // 润色按钮可以立即启用
        allStyleButtons.forEach(btn => {
            if (btn) btn.disabled = false;
        });

    } catch (error) {
        loadingDiv.style.display = 'none';
        showError(outputDiv, `润色失败: ${error.message}`);
        console.error('API请求错误:', error);
        // 出错时重新启用所有按钮
        document.getElementById('polishBtn').disabled = false;
        document.getElementById('copyBtn').disabled = true;
        document.getElementById('replaceBtn').disabled = true;
    }
}

// 显示错误信息
function showError(element, message) {
    // 获取思考过程框
    const thinkingDiv = document.getElementById('thinkingProcess');

    // 隐藏思考过程框，显示结果框
    thinkingDiv.style.display = 'none';
    element.style.display = 'block';

    // 直接显示错误信息，不使用打字机效果
    element.innerHTML = message;
    element.className = 'result-box error';

    // 获取所有润色风格按钮
    const allStyleButtons = [
        document.getElementById('polishBtn'),
        document.getElementById('expandBtn'),
        document.getElementById('condenseBtn'),
        document.getElementById('politicalBtn')
    ];

    // 确保润色按钮可用，其他按钮禁用
    allStyleButtons.forEach(btn => {
        if (btn) btn.disabled = false;
    });
    document.getElementById('copyBtn').disabled = true;
    document.getElementById('replaceBtn').disabled = true;
}

// 调用GPUStack API进行文本润色 - 使用增量更新实时显示内容
async function callPolishAPI(text, style = 'formal') {
    // 显示调试信息
    const debugDiv = document.getElementById('statusMessage');
    const outputDiv = document.getElementById('polishedText');
    const thinkingDiv = document.getElementById('thinkingProcess');
    const thinkingContent = thinkingDiv.querySelector('.thinking-content');

    debugDiv.innerText = '正在连接API...';
    debugDiv.style.color = '#666';

    // 清空输出区域和思考过程区域，准备接收新内容
    outputDiv.innerText = '';
    thinkingContent.innerText = '';

    // 显示思考过程框，隐藏结果框
    thinkingDiv.style.display = 'block';
    outputDiv.style.display = 'none';

    // API配置 - 使用GPUStack API
    const API_URL = "/api/polish";

    // 根据不同的润色风格生成不同的提示语
    let prompt = '';

    switch(style) {
        case 'polish':
            prompt = `<think>
请先思考如何对以下文本进行基础润色，分析文本中可能存在的语法错误、逻辑问题和标点符号使用错误。
思考需要修改的地方，以及如何保持原文的风格和语调。
</think>

请对以下文本进行基础润色，仅检查并修正语法错误、逻辑问题和标点符号使用错误，不要对原文进行大幅度改变。保持原文的风格和语调，只做必要的修正。

${text}`;
            break;
        case 'expand':
            prompt = `<think>
请先思考如何对以下文本进行扩写，分析文本的主要内容和结构，考虑可以添加哪些细节、例子和解释，使其更加充实和全面。
思考如何保持原文的风格和语调，同时增加内容的丰富度。
</think>

你的任务是将以下文本进行扩写，添加更多细节、例子和解释，使其更加充实和全面。

原文：
${text}

扩写后的文本：`;
            break;
        case 'condense':
            prompt = `<think>
请先思考如何对以下文本进行精简缩写，分析文本的核心信息和冗余内容，考虑如何在保留核心信息的同时减少冗余内容。
思考如何使文本更加简洁和直接，同时不丢失重要信息。
</think>

你的任务是将以下文本进行精简缩写，保留核心信息的同时减少冗余内容，使其更加简洁和直接。

原文：
${text}

缩写后的文本：`;
            break;
        case 'political':
            prompt = `<think>
请先思考如何将以下文本改写成党政风格，分析文本的主要内容和结构，考虑如何使用更加正胆、庄重的语言，以及可以添加哪些政治术语和典型表达。
思考如何保持原文的核心信息，同时增加党政风格的特点。
</think>

请将以下文本改写成党政风格，使用更加正胆、庄重的语言，并适当增加一些政治术语和典型表达。

${text}`;
            break;
        default:
            prompt = `<think>
请先思考如何对以下文本进行基础润色，分析文本中可能存在的语法错误、逻辑问题和标点符号使用错误。
思考需要修改的地方，以及如何保持原文的风格和语调。
</think>

请对以下文本进行基础润色，仅检查并修正语法错误、逻辑问题和标点符号使用错误，不要对原文进行大幅度改变。保持原文的风格和语调，只做必要的修正。

${text}`;
    }

    try {
        debugDiv.innerText = '发送请求中...';

        // 根据不同的润色风格设置不同的参数
        let temperature, top_p, frequency_penalty, presence_penalty;

        switch(style) {
            case 'polish':
                temperature = 0.3;
                top_p = 0.8;
                frequency_penalty = 0.1;
                presence_penalty = 0.1;
                break;
            case 'expand':
                temperature = 0.6;
                top_p = 0.9;
                frequency_penalty = 0.2;
                presence_penalty = 0.2;
                break;
            case 'condense':
                temperature = 0.4;
                top_p = 0.8;
                frequency_penalty = 0.1;
                presence_penalty = 0.1;
                break;
            case 'political':
                temperature = 0.5;
                top_p = 0.9;
                frequency_penalty = 0.2;
                presence_penalty = 0.1;
                break;
            default:
                temperature = 0.3;
                top_p = 0.8;
                frequency_penalty = 0.1;
                presence_penalty = 0.1;
        }

        // 准备请求体 - 使用GPUStack API的格式
        const requestBody = {
            messages: [
                {
                    role: "user",
                    content: prompt
                }
            ],
            model: "deepseek-r1",
            stream: true,
            temperature: temperature,
            top_p: top_p,
            frequency_penalty: frequency_penalty,
            presence_penalty: presence_penalty,
            max_tokens: 4096
        };

        console.log('发送API请求:', API_URL);
        console.log('请求体:', JSON.stringify(requestBody, null, 2));

        // 发送API请求
        const response = await fetch(API_URL, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`API请求失败: ${response.status} - ${errorData}`);
        }

        debugDiv.innerText = '接收响应中...';

        // 使用 ReadableStream 读取数据
        console.log('初始化 ReadableStream 读取器');
        const reader = response.body.getReader();
        const decoder = new TextDecoder("utf-8", { fatal: false, ignoreBOM: true });
        let fullContent = ''; // 用于累积完整内容
        let buffer = ''; // 用于存储不完整的数据行
        let readCount = 0;

        // 用于跟踪是否已经开始显示最终结果
        let isShowingFinalResult = false;
        // 用于存储最终结果的内容
        let finalResultContent = '';

        while (true) {
            console.log(`读取数据块 #${++readCount}`);
            const { done, value } = await reader.read();

            if (done) {
                console.log('数据读取完成');
                break;
            }

            // 解码二进制数据，确保正确处理中文字符和标点符号
            let chunk;
            try {
                // 使用stream:true可能会导致字符被截断，特别是多字节字符（如中文和某些标点）
                // 为了避免这个问题，我们先尝试不使用stream模式解码
                chunk = decoder.decode(value, { stream: false });

                // 如果解码失败或包含替换字符，再尝试使用stream模式
                if (chunk.includes('�')) {
                    console.log('检测到替换字符，尝试使用stream模式重新解码');
                    chunk = decoder.decode(value, { stream: true });
                }
            } catch (e) {
                console.error('解码数据块时出错:', e);
                // 使用更宽松的解码方式重试
                chunk = new TextDecoder('utf-8', { fatal: false }).decode(value);
            }

            // 检查并替换无效字符，但保留有效的标点符号
            // 只替换独立的替换字符，不替换可能是有效字符一部分的替换字符
            chunk = chunk.replace(/([^\u0000-\u007F])�/g, '$1'); // 保留前面的非ASCII字符
            chunk = chunk.replace(/�([^\u0000-\u007F])/g, '$1'); // 保留后面的非ASCII字符
            chunk = chunk.replace(/�/g, ''); // 移除剩余的替换字符

            console.log('解码后的数据块:', chunk.substring(0, 50) + (chunk.length > 50 ? '...' : ''));
            buffer += chunk;

            // 按行分割数据
            const lines = buffer.split('\n');
            buffer = lines.pop() || ''; // 最后一行可能不完整，保存到buffer中

            // 处理每一行数据
            for (const line of lines) {
                if (line.trim() === '' || line.trim() === 'data: [DONE]') continue;

                if (line.startsWith('data: ')) {
                    try {
                        // 提取JSON数据
                        const jsonData = JSON.parse(line.substring(6));

                        // 提取内容
                        let content = '';

                        // 检查是否是最终完整结果数据包
                        if (jsonData.object === "chat.completion" && jsonData.choices && jsonData.choices.length > 0 &&
                            jsonData.choices[0].message && jsonData.choices[0].message.content) {
                            // 这是最终完整结果，但我们已经增量显示了内容，所以不需要再处理
                            console.log('收到最终完整结果数据包，但已经增量显示了内容');
                            continue;
                        }

                        // 从delta中提取内容
                        if (jsonData.choices && jsonData.choices.length > 0 && jsonData.choices[0].delta) {
                            const delta = jsonData.choices[0].delta;
                            if (delta.content) {
                                content = delta.content;
                            }
                        }
                        // 如果没有delta.content，尝试其他可能的字段
                        else if (jsonData.choices && jsonData.choices.length > 0) {
                            if (jsonData.choices[0].message && jsonData.choices[0].message.content) {
                                content = jsonData.choices[0].message.content;
                            } else if (jsonData.choices[0].text) {
                                content = jsonData.choices[0].text;
                            }
                        }

                        // 如果提取到内容，则更新显示
                        if (content) {
                            fullContent += content;

                            // 处理思考过程和最终结果
                            const thinkingDiv = document.getElementById('thinkingProcess');
                            const thinkingContent = thinkingDiv.querySelector('.thinking-content');

                            // 查找最后一个</think>标签的位置
                            const thinkEndIndex = fullContent.lastIndexOf('</think>');

                            if (thinkEndIndex !== -1 && !isShowingFinalResult) {
                                // 首次找到</think>标签，开始显示最终结果
                                isShowingFinalResult = true;

                                // 分离思考过程和最终结果
                                const thinkingProcess = fullContent.substring(0, thinkEndIndex + 8);
                                finalResultContent = fullContent.substring(thinkEndIndex + 8);

                                // 更新思考过程（只需更新一次）
                                thinkingContent.innerText = thinkingProcess;

                                // 显示结果框，隐藏思考过程框
                                thinkingDiv.style.display = 'none';
                                outputDiv.style.display = 'block';

                                // 初始显示结果
                                outputDiv.innerText = finalResultContent;

                                // 启用复制和替换按钮
                                document.getElementById('copyBtn').disabled = false;
                                document.getElementById('replaceBtn').disabled = false;
                            }
                            else if (isShowingFinalResult) {
                                // 已经在显示最终结果，只需增量更新结果内容
                                // 提取新增的最终结果内容
                                const newFinalContent = fullContent.substring(thinkEndIndex + 8);
                                finalResultContent = newFinalContent;

                                // 直接更新结果，不进行格式化处理（提高性能）
                                outputDiv.innerText = finalResultContent;

                                // 滚动到底部
                                outputDiv.scrollTop = outputDiv.scrollHeight;
                            }
                            else {
                                // 没有找到</think>标签，更新思考过程
                                thinkingContent.innerText = fullContent;

                                // 确保思考过程框可见，结果框隐藏
                                thinkingDiv.style.display = 'block';
                                outputDiv.style.display = 'none';

                                // 滚动思考过程框到底部
                                thinkingContent.scrollTop = thinkingContent.scrollHeight;
                            }
                        }
                    } catch (e) {
                        console.error('解析数据行失败:', e, line);
                    }
                }
            }
        }

        // 数据读取完成后，进行最终处理
        // 如果没有提取到任何内容，显示错误消息
        if (!fullContent) {
            console.error("未能提取到任何内容");
            thinkingDiv.style.display = 'none';
            outputDiv.style.display = 'block';
            outputDiv.innerText = "无法获取润色结果，请重试";
            return "无法获取润色结果，请重试";
        }

        // 如果还没有开始显示最终结果，现在处理
        if (!isShowingFinalResult) {
            // 查找最后一个</think>标签的位置，提取最终结果
            const thinkEndIndex = fullContent.lastIndexOf('</think>');
            let finalResult = fullContent;

            if (thinkEndIndex !== -1) {
                // 找到了</think>标签，只使用它之后的内容作为最终结果
                finalResult = fullContent.substring(thinkEndIndex + 8);
                console.log('最终结果提取完成，长度:', finalResult.length);
            }

            // 格式化内容：每段开头缩进2格，不输出空行
            const formattedResult = formatTextWithIndent(finalResult);
            outputDiv.innerText = formattedResult;

            // 隐藏思考过程框，显示结果框
            thinkingDiv.style.display = 'none';
            outputDiv.style.display = 'block';
        } else {
            // 已经在显示最终结果，只需要在最后进行一次格式化
            // 这样可以确保最终显示的文本格式正确
            const formattedResult = formatTextWithIndent(finalResultContent);
            outputDiv.innerText = formattedResult;
        }

        // 确保按钮已启用
        document.getElementById('copyBtn').disabled = false;
        document.getElementById('replaceBtn').disabled = false;

        debugDiv.innerText = '处理完成';
        setTimeout(() => { debugDiv.innerText = ''; }, 2000);

        // 返回最终结果（已格式化）
        return isShowingFinalResult ? formatTextWithIndent(finalResultContent) : formatTextWithIndent(fullContent);

    } catch (error) {
        console.error('API调用失败:', error);
        debugDiv.innerText = `错误: ${error.message}`;
        debugDiv.style.color = 'red';

        // 隐藏思考过程框，显示结果框
        thinkingDiv.style.display = 'none';
        outputDiv.style.display = 'block';
        outputDiv.innerText = `无法连接到润色服务: ${error.message}`;
        outputDiv.className = 'result-box error';

        throw new Error(`无法连接到润色服务: ${error.message}`);
    }
}

// 复制润色后的文本到剪贴板
function copyPolishedText() {
    const polishedText = document.getElementById('polishedText').innerText;
    const statusMsg = document.getElementById('statusMessage');

    if (!polishedText) {
        statusMsg.innerText = '没有可复制的内容';
        statusMsg.className = 'status-message status-error';
        return;
    }

    try {
        // 使用兼容性更好的方法复制文本
        // 1. 创建一个临时的文本区域元素
        const textArea = document.createElement('textarea');
        textArea.value = polishedText;

        // 2. 设置样式使其不可见
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);

        // 3. 选中文本并执行复制命令
        textArea.focus();
        textArea.select();

        try {
            // 使用异步剪贴板API，避免使用已弃用的document.execCommand
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(polishedText)
                    .then(() => {
                        statusMsg.innerText = '已复制到剪贴板';
                        statusMsg.className = 'status-message status-success';
                        setTimeout(() => {
                            statusMsg.innerText = '';
                            statusMsg.className = 'status-message';
                        }, 2000);
                    })
                    .catch(err => {
                        throw err;
                    });
            } else {
                // 如果新API不可用，回退到旧方法
                const successful = document.execCommand('copy');
                document.body.removeChild(textArea);

                if (successful) {
                    statusMsg.innerText = '已复制到剪贴板';
                    statusMsg.className = 'status-message status-success';
                } else {
                    throw new Error('复制命令执行失败');
                }
            }
        } catch (err) {
            document.body.removeChild(textArea);
            throw err;
        }

        // 注意：如果使用新API，已经在回调中设置了超时清除
        // 这里只在使用旧方法时才需要清除
        if (!navigator.clipboard || !navigator.clipboard.writeText) {
            setTimeout(() => {
                statusMsg.innerText = '';
                statusMsg.className = 'status-message';
            }, 2000);
        }
    } catch (err) {
        console.error('复制失败:', err);
        statusMsg.innerText = `复制失败: ${err.message || '未知错误'}`;
        statusMsg.className = 'status-message status-error';

        // 尝试备用方法
        try {
            // 如果第一种方法失败，尝试使用剪贴板API
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(polishedText)
                    .then(() => {
                        statusMsg.innerText = '已复制到剪贴板';
                        statusMsg.className = 'status-message status-success';
                        setTimeout(() => {
                            statusMsg.innerText = '';
                            statusMsg.className = 'status-message';
                        }, 2000);
                    })
                    .catch(clipErr => {
                        console.error('剪贴板API复制失败:', clipErr);
                        // 显示手动复制提示
                        showManualCopyTip(statusMsg);
                    });
            } else {
                // 如果剪贴板API不可用，显示手动复制提示
                showManualCopyTip(statusMsg);
            }
        } catch (backupErr) {
            console.error('备用复制方法失败:', backupErr);
            // 显示手动复制提示
            showManualCopyTip(statusMsg);
        }
    }
}

// 更新字符计数
function updateCharCount() {
    const text = document.getElementById('inputText').value;
    const count = text.length;
    document.getElementById('charCount').innerText = `字符数: ${count}`;
}

// 替换原文本的函数
function replaceOriginalText() {
    const polishedText = document.getElementById('polishedText').innerText;
    const statusMsg = document.getElementById('statusMessage');
    const replaceBtn = document.getElementById('replaceBtn');

    if (!polishedText) {
        statusMsg.innerText = '没有可替换的内容';
        statusMsg.className = 'status-message status-error';
        return;
    }

    // 检查是否有保存的原始选择位置
    if (!originalSelectionRange) {
        statusMsg.innerText = '无法找到原始选择位置，请重新选择文本';
        statusMsg.className = 'status-message status-error';
        return;
    }

    // 禁用按钮并显示状态
    replaceBtn.disabled = true;
    statusMsg.innerText = '正在替换原文...';
    statusMsg.className = 'status-message';

    // 使用setTimeout来确保状态更新先显示出来
    setTimeout(() => {
        try {
            // 检查是否有Application对象
            if (window.Application && window.Application.ActiveDocument) {
                // 使用保存的原始选择位置
                if (originalSelectionRange) {
                    // 保存当前的选择区域（如果有），以便在替换后恢复
                    let currentSelection = null;
                    try {
                        currentSelection = window.Application.Selection.Range;
                        console.log('已保存当前选择区域位置');
                    } catch (e) {
                        console.log('无法保存当前选择区域:', e);
                    }

                    // 选中原始区域
                    try {
                        originalSelectionRange.Select();
                        console.log('已选中原始区域');
                    } catch (e) {
                        console.error('选中原始区域失败:', e);
                        throw new Error('无法选中原始区域: ' + e.message);
                    }

                    // 使用全新的方法替换文本，确保只替换选中的文本
                    try {
                        // 获取WPS应用程序对象
                        const app = window.Application;
                        const doc = app.ActiveDocument;
                        const selection = app.Selection;

                        // 保存原始选择区域的起始和结束位置
                        const startPos = originalSelectionRange.Start;
                        const endPos = originalSelectionRange.End;
                        console.log(`原始选择区域位置: 从 ${startPos} 到 ${endPos}`);

                        // 选中原始区域
                        originalSelectionRange.Select();
                        console.log('已选中原始区域');

                        // 使用精确的方法替换文本
                        // 方法1: 使用TypeText方法
                        try {
                            // 确保润色后的文本段落格式正确
                            // 将文本按段落分割，确保每段都有正确的缩进
                            const paragraphs = polishedText.split('\n');
                            const formattedParagraphs = paragraphs.map(para => {
                                // 去除可能已存在的缩进
                                const trimmedPara = para.replace(/^[\s　]+/, '');
                                // 如果不是空行，添加缩进
                                if (trimmedPara.length > 0) {
                                    return `　　${trimmedPara}`;
                                }
                                return para;
                            });

                            // 将格式化后的文本组合成一个字符串，使用段落标记分隔，过滤空行
                            const formattedText = formattedParagraphs
                                .filter(para => para.trim().length > 0)
                                .join('\r\n');

                            // 在文本末尾添加一个换行符
                            const textWithNewline = formattedText + '\r\n';

                            // 先删除选中的文本
                            selection.Delete();
                            // 然后一次性插入格式化后的文本
                            selection.TypeText(textWithNewline);
                            console.log('使用TypeText方法替换成功');
                        } catch (typeTextError) {
                            console.error('TypeText方法失败:', typeTextError);

                            // 方法2: 尝试使用剪贴板方法
                            try {
                                // 确保润色后的文本段落格式正确
                                // 将文本按段落分割，确保每段都有正确的缩进
                                const paragraphs = polishedText.split('\n');
                                const formattedParagraphs = paragraphs.map(para => {
                                    // 去除可能已存在的缩进
                                    const trimmedPara = para.replace(/^[\s　]+/, '');
                                    // 如果不是空行，添加缩进
                                    if (trimmedPara.length > 0) {
                                        return `　　${trimmedPara}`;
                                    }
                                    return para;
                                });

                                // 将格式化后的文本组合成一个字符串，使用段落标记分隔，过滤空行
                                const formattedText = formattedParagraphs
                                    .filter(para => para.trim().length > 0)
                                    .join('\r\n');

                                // 在文本末尾添加一个换行符
                                const textWithNewline = formattedText + '\r\n';

                                // 先将格式化后的文本复制到剪贴板
                                const oldClipboard = app.System.ClipboardText;
                                app.System.ClipboardText = textWithNewline;

                                // 选中原始区域
                                doc.Range(startPos, endPos).Select();

                                // 删除选中内容并粘贴
                                selection.Delete();
                                selection.Paste();

                                // 恢复剪贴板原有内容
                                app.System.ClipboardText = oldClipboard;
                                console.log('使用剪贴板方法替换成功');
                            } catch (clipboardError) {
                                console.error('剪贴板方法失败:', clipboardError);

                                // 方法3: 尝试使用最原始的方法
                                try {
                                    // 确保润色后的文本段落格式正确
                                    // 将文本按段落分割，确保每段都有正确的缩进
                                    const paragraphs = polishedText.split('\n');
                                    const formattedParagraphs = paragraphs.map(para => {
                                        // 去除可能已存在的缩进
                                        const trimmedPara = para.replace(/^[\s　]+/, '');
                                        // 如果不是空行，添加缩进
                                        if (trimmedPara.length > 0) {
                                            return `　　${trimmedPara}`;
                                        }
                                        return para;
                                    });

                                    // 将格式化后的文本组合成一个字符串，使用段落标记分隔，过滤空行
                                    const formattedText = formattedParagraphs
                                        .filter(para => para.trim().length > 0)
                                        .join('\r\n');

                                    // 在文本末尾添加一个换行符
                                    const textWithNewline = formattedText + '\r\n';

                                    // 重新选中原始区域
                                    doc.Range(startPos, endPos).Select();
                                    // 直接设置Text属性
                                    selection.Text = textWithNewline;
                                    console.log('使用Text属性方法替换成功');
                                } catch (textError) {
                                    console.error('Text属性方法失败:', textError);
                                    throw new Error('所有替换方法均失败');
                                }
                            }
                        }

                        console.log('成功替换原文为润色后的文本');
                    } catch (e) {
                        console.error('替换文本失败:', e);
                        throw new Error('无法替换文本: ' + e.message);
                    }

                    // 如果用户在润色过程中移动了光标，尝试恢复到原来的位置
                    if (currentSelection) {
                        try {
                            currentSelection.Select();
                            console.log('已恢复到用户当前的光标位置');
                        } catch (e) {
                            console.log('恢复光标位置失败，但这不影响替换操作:', e);
                        }
                    }

                    statusMsg.innerText = '原文已替换成功';
                    statusMsg.className = 'status-message status-success';
                    setTimeout(() => {
                        statusMsg.innerText = '';
                        statusMsg.className = 'status-message';
                    }, 2000);

                    // 清除原始选择位置，防止重复替换
                    originalSelectionRange = null;
                    originalSelectionText = "";

                    // 替换成功后保持按钮禁用状态，防止重复替换
                    replaceBtn.disabled = true;
                } else {
                    throw new Error('无法获取原始选中区域');
                }
            } else {
                throw new Error('无法访问WPS文档');
            }
        } catch (error) {
            console.error('替换原文失败:', error);
            statusMsg.innerText = `替换原文失败: ${error.message}`;
            statusMsg.className = 'status-message status-error';

            // 如果出错，清除原始选择位置，让用户重新选择
            originalSelectionRange = null;
            originalSelectionText = "";

            // 出错时重新启用按钮，允许用户重试
            replaceBtn.disabled = false;
        }
    }, 100); // 小延时确保 UI 更新
}

// 清空所有内容并重置表单
function clearAll() {
    // 清空输入框
    document.getElementById('inputText').value = '';

    // 直接清空结果区域，不使用打字机效果
    document.getElementById('polishedText').innerHTML = '';
    document.getElementById('polishedText').className = 'result-box';

    // 重置状态消息
    document.getElementById('statusMessage').innerText = '';
    document.getElementById('statusMessage').className = 'status-message';

    // 禁用复制和替换按钮
    document.getElementById('copyBtn').disabled = true;
    document.getElementById('replaceBtn').disabled = true;

    // 清除原始选择位置
    originalSelectionRange = null;
    originalSelectionText = "";

    // 更新字符计数
    updateCharCount();
}

// 获取WPS文档中选中的文本并保存选择位置
function getSelectedText() {
    try {
        // 检查是否有Application对象
        if (window.Application && window.Application.Selection) {
            const selection = window.Application.Selection;

            // 获取选择类型（如果可用）
            let selectionType = null;
            try {
                selectionType = selection.Type;
                console.log('选择类型:', selectionType);

                // 检查是否真的选中了文本
                // WPS中，wdSelectionIP表示插入点（光标），没有选中文本
                // 只有wdSelectionNormal才表示真正选中了文本
                if (window.Application.Enum && selectionType === window.Application.Enum.WdSelectionType.wdSelectionIP) {
                    console.log('光标位置，没有选中文本');
                    return null;
                }
            } catch (e) {
                console.log('无法获取选择类型:', e);
            }

            const selectedText = selection.Text;
            console.log('选中文本:', selectedText, '长度:', selectedText ? selectedText.length : 0);

            // 检查是否是有效的选择
            // 1. 文本必须存在且非空
            // 2. 文本长度必须大于1（避免单个字符的选择）
            // 3. 选择的起始位置和结束位置必须不同（确保真的选中了文本）
            const hasValidSelection = selectedText && selectedText.trim() && selectedText.trim().length > 1;

            // 额外检查选择范围
            let hasValidRange = false;
            try {
                const range = selection.Range;
                hasValidRange = range.Start !== range.End;
                console.log(`选择范围: 从 ${range.Start} 到 ${range.End}, 是否有效范围: ${hasValidRange}`);
            } catch (e) {
                console.log('无法获取选择范围:', e);
            }

            if (hasValidSelection && hasValidRange) {
                const trimmedText = selectedText.trim();

                // 保存原始选择的位置信息
                try {
                    // 保存选择区域的范围对象
                    originalSelectionRange = selection.Range;
                    // 保存选中的文本
                    originalSelectionText = trimmedText;

                    console.log('已保存有效的原始选择位置');
                } catch (rangeError) {
                    console.error('保存选择位置失败:', rangeError);
                }

                return trimmedText;
            } else {
                console.log('没有有效的选中文本或仅有单个字符或无效的选择范围');
            }
        }
    } catch (error) {
        console.error('获取选中文本失败:', error);
    }
    return null;
}

// 设置输入框的文本
function setInputText(text) {
    const inputElement = document.getElementById('inputText');
    if (text) {
        // 处理文本，确保第一段有正确的缩进
        // 检查文本是否已经有缩进
        if (!text.startsWith('　　') && !text.startsWith('  ')) {
            // 如果没有缩进，添加全角空格缩进
            const paragraphs = text.split('\n');
            // 为第一段添加缩进
            if (paragraphs.length > 0 && paragraphs[0].trim().length > 0) {
                paragraphs[0] = '　　' + paragraphs[0].trim();
            }
            // 处理其他段落
            for (let i = 1; i < paragraphs.length; i++) {
                if (paragraphs[i].trim().length > 0 && !paragraphs[i].startsWith('　　') && !paragraphs[i].startsWith('  ')) {
                    paragraphs[i] = '　　' + paragraphs[i].trim();
                }
            }
            text = paragraphs.join('\n');
        }

        // 清除默认文本并设置新文本
        inputElement.value = text;
        // 清除之前的润色结果
        document.getElementById('polishedText').innerHTML = '';
        document.getElementById('polishedText').className = 'result-box';
        // 重置状态消息
        document.getElementById('statusMessage').innerText = '';
        document.getElementById('statusMessage').className = 'status-message';
    }
}

// 获取语言名称的函数
function getLanguageName(langCode) {
    const languageMap = {
        'zh': '中文',
        'en': '英文',
        'ja': '日语',
        'ko': '韩语',
        'fr': '法语',
        'de': '德语',
        'es': '西班牙语',
        'it': '意大利语',
        'ru': '俄语',
        'pt': '葡萄牙语',
        'ar': '阿拉伯语',
        'hi': '印地语',
        'bn': '孟加拉语',
        'pa': '旁遮普语',
        'te': '泰卢固语',
        'mr': '马拉地语',
        'ta': '泰米尔语',
        'ur': '乌尔都语',
        'gu': '古吉拉特语',
        'kn': '卡纳达语',
        'ml': '马拉雅拉姆语',
        'vi': '越南语',
        'th': '泰语',
        'tr': '土耳其语',
        'nl': '荷兰语',
        'pl': '波兰语',
        'sv': '瑞典语',
        'no': '挪威语',
        'da': '丹麦语',
        'fi': '芬兰语',
        'cs': '捷克语',
        'hu': '匈牙利语',
        'el': '希腊语',
        'he': '希伯来语',
        'id': '印尼语',
        'ms': '马来语',
        'fa': '波斯语',
        'uk': '乌克兰语',
        'ro': '罗马尼亚语',
        'bg': '保加利亚语',
        'hr': '克罗地亚语',
        'sr': '塞尔维亚语',
        'sk': '斯洛伐克语',
        'sl': '斯洛文尼亚语',
        'et': '爱沙尼亚语',
        'lv': '拉脱维亚语',
        'lt': '立陶宛语'
    };

    return languageMap[langCode] || langCode;
}

// 格式化文本，确保每段开头缩进，段落间无空行
function formatTextWithIndent(text) {
    if (!text) return text;

    // 检查文本长度，如果太长可能会影响性能
    const isLongText = text.length > 5000;

    // 对于非常长的文本，使用更简单的处理方式以提高性能
    if (isLongText) {
        // 简化的格式化处理：只处理明显的段落分隔
        return text.split('\n')
            .filter(line => line.trim().length > 0)  // 过滤空行
            .map(line => {
                // 只处理没有缩进的行
                if (!line.startsWith('　') && !line.startsWith(' ')) {
                    return `　　${line.trim()}`;
                }
                return line;
            })
            .join('\n');
    }

    // 对于较短的文本，使用更精确的处理
    // 先将文本按段落分割（可能有多个\n或空行）
    const paragraphs = text.split(/\n+/).filter(p => p.trim().length > 0);

    // 处理每一段，添加缩进
    const formattedParagraphs = paragraphs.map(p => {
        // 先去除可能已存在的缩进，但保留标点符号
        // 使用更精确的方式去除开头的空白，而不是使用trim()
        const trimmedParagraph = p.replace(/^[\s　]+/, '');

        // 检查段落是否已经有缩进
        if (trimmedParagraph.startsWith('　　')) {
            return trimmedParagraph;
        }

        // 添加两个中文空格作为缩进
        return `　　${trimmedParagraph}`;
    });

    // 将所有段落用\n连接（无空行）
    return formattedParagraphs.join('\n');
}

// 翻译功能已移至translation.js

// 将函数暴露为全局变量，以便其他脚本可以访问
window.ribbonCopyText = copyPolishedText;
window.ribbonReplaceText = replaceOriginalText;
window.ribbonPolish = callPolishAPI;

// 添加一个全局函数，用于检查ribbon.js是否已加载
window.isRibbonLoaded = function() {
    return true;
};

console.log('ribbon.js: 已暴露全局函数 ribbonCopyText, ribbonReplaceText, ribbonPolish');
