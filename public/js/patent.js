// 专利申请技术交底书生成功能实现

// 全局变量
let progressInterval;

// DOM元素引用
let projectNameInput;
let generateButton;
let progressContainer;
let progressBar;
let loadingIndicator;
let resultContainer;
let resultContent;
let insertButton;
let copyButton;
let statusMessage;
let statusIndicator;
let statusText;

// 初始化函数
function init() {
    // 获取DOM元素
    projectNameInput = document.getElementById('projectName');
    generateButton = document.getElementById('generateButton');
    progressContainer = document.getElementById('progressContainer');
    progressBar = document.getElementById('progressBar');
    loadingIndicator = document.getElementById('loadingIndicator');
    resultContainer = document.getElementById('resultContainer');
    resultContent = document.getElementById('resultContent');
    insertButton = document.getElementById('insertButton');
    copyButton = document.getElementById('copyButton');
    statusMessage = document.getElementById('statusMessage');
    statusIndicator = document.getElementById('statusIndicator');
    statusText = document.getElementById('statusText');

    // 初始化滚动控制变量
    window.userScrolledThinking = false;
    window.userScrolledResult = false;

    // 初始化思考过程容器
    const thinkingContainer = document.getElementById('thinkingContainer');
    const thinkingContent = document.getElementById('thinkingContent');

    if (thinkingContainer) {
        thinkingContainer.style.display = 'none';
    }

    if (thinkingContent) {
        thinkingContent.innerHTML = '';
    }

    // 检查URL参数中是否有项目名称
    const urlParams = new URLSearchParams(window.location.search);
    const initialProjectName = urlParams.get('projectName');

    // 如果有项目名称参数，并且不是空或只有一个字符，才自动填充
    if (initialProjectName) {
        const decodedName = decodeURIComponent(initialProjectName);
        // 确保项目名称不是空或只有一个字符
        if (decodedName.trim().length > 1) {
            projectNameInput.value = decodedName;
        } else {
            // 如果项目名称为空或只有一个字符，清空输入框
            projectNameInput.value = '';
        }
    }



    // 设置事件监听器
    generateButton.addEventListener('click', generatePatentDisclosure);
    insertButton.addEventListener('click', insertToDocument);
    copyButton.addEventListener('click', copyContent);
    projectNameInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            generatePatentDisclosure();
        }
    });

    // 添加滚动事件监听器，检测用户是否手动滚动
    const thinkingContentElement = document.getElementById('thinkingContent');
    if (thinkingContentElement) {
        thinkingContentElement.addEventListener('scroll', function() {
            // 检测用户是否手动滚动
            // 如果滚动位置不在底部，说明用户手动滚动了
            const isAtBottom = this.scrollHeight - this.scrollTop <= this.clientHeight + 50; // 添加50px的容差
            window.userScrolledThinking = !isAtBottom;
        });
    }

    // 为结果容器添加滚动事件监听器
    if (resultContent) {
        resultContent.addEventListener('scroll', function() {
            // 检测用户是否手动滚动
            const isAtBottom = this.scrollHeight - this.scrollTop <= this.clientHeight + 50; // 添加50px的容差
            window.userScrolledResult = !isAtBottom;
        });
    }

    // 禁用复制和插入按钮，直到生成完成
    insertButton.disabled = true;
    copyButton.disabled = true;
}

// 检查服务器连接状态
async function checkServerConnection() {
    try {
        updateServerStatus('checking');
        console.log('正在检查与GPUStack API服务器的连接...');

        // 检查API端点 - 使用专利API端点而不是翻译API端点
        const API_URL = "/api/gpustack/patent";
        console.log('测试连接到API端点:', API_URL);

        // 设置超时
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

        try {
            // 发送简单的POST请求测试连接
            const response = await fetch(API_URL, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    messages: [
                        {
                            role: "user",
                            content: "测试连接"
                        }
                    ],
                    model: "deepseek-r1",
                    stream: false,
                    max_tokens: 10 // 限制生成的token数量，减少资源消耗
                }),
                signal: controller.signal
            }).catch(e => {
                console.error('连接测试失败:', e);
                throw e;
            });

            // 清除超时
            clearTimeout(timeoutId);

            if (response.ok) {
                console.log('服务器连接成功');
                updateServerStatus('connected');
            } else {
                console.error('服务器连接失败，状态码:', response.status);
                updateServerStatus('error', '服务器连接失败');
            }
        } catch (error) {
            // 清除超时
            clearTimeout(timeoutId);
            console.error('服务器连接测试失败:', error);
            updateServerStatus('error', '连接失败');
        }
    } catch (error) {
        console.error('连接检查完全失败:', error);
        updateServerStatus('error', error.message);
    }
}

// 更新服务器状态指示器
function updateServerStatus(status, message) {
    if (statusIndicator && statusText) {
        // 设置状态颜色
        switch(status) {
            case 'checking':
                statusIndicator.style.backgroundColor = '#999'; // 灰色
                statusText.textContent = '检测中...';
                break;
            case 'connected':
                statusIndicator.style.backgroundColor = '#4CAF50'; // 绿色
                statusText.textContent = '已连接';
                break;
            case 'error':
                statusIndicator.style.backgroundColor = '#F44336'; // 红色
                statusText.textContent = '连接失败';
                break;
            default:
                statusIndicator.style.backgroundColor = '#999';
                statusText.textContent = '未知状态';
        }

        // 如果有额外消息，显示在状态文本中
        if (message) {
            statusText.textContent += ': ' + message;
        }
    }
}

// 将格式化文本转换为HTML
function convertToHtml(text) {
    // 将文本分成段落进行处理
    const paragraphs = text.split(/\n\s*\n/);
    let processedHtml = '';

    // 处理每个段落
    for (let j = 0; j < paragraphs.length; j++) {
        const para = paragraphs[j].trim();
        if (!para) continue;

        // 检测是否是章节标题（只匹配"一、"到"九、"格式的标题）
        if (/^(一、|二、|三、|四、|五、|六、|七、|八、|九、)/.test(para)) {
            // 章节标题单独成段，加粗显示，确保没有背景色
            processedHtml += '<h3 style="margin-top: 20px; margin-bottom: 15px; font-weight: bold; background: none; background-color: transparent;">' + para + '</h3>';
        } else {
            // 普通段落，添加缩进，确保不加粗，确保没有背景色
            processedHtml += '<p style="margin-bottom: 10px; font-weight: normal; background: none; background-color: transparent;">' + para.replace(/\n/g, '<br>') + '</p>';
        }
    }

    return processedHtml;
}

// 格式化并显示文本 (保留旧函数以兼容现有代码)
function typeWriter(element, text) {
    // 先禁用复制和插入按钮，等处理完成后再启用
    insertButton.disabled = true;
    copyButton.disabled = true;
    element.innerHTML = '';

    // 格式化文本
    const formattedText = formatPatentText(text);

    // 转换为HTML
    const processedHtml = convertToHtml(formattedText);

    // 将处理后的HTML内容直接设置到元素中
    element.innerHTML = processedHtml;

    // 启用复制和插入按钮
    insertButton.disabled = false;
    copyButton.disabled = false;
}



// 格式化文本，处理Markdown标记和段落格式
function formatPatentText(text) {
    // 清理可能的Markdown标记
    let formattedText = text.replace(/#{1,6}\s/g, ''); // 移除标题标记 (#, ##, ###等)
    formattedText = formattedText.replace(/\*\*\*(.*?)\*\*\*/g, '$1'); // 移除粗斜体标记 ***text***
    formattedText = formattedText.replace(/\*\*(.*?)\*\*/g, '$1'); // 移除粗体标记 **text**
    formattedText = formattedText.replace(/\*(.*?)\*/g, '$1'); // 移除斜体标记 *text*

    // 确保章节标题和内容之间有正确的分隔
    formattedText = formattedText.replace(/(一、|二、|三、|四、|五、|六、|七、|八、|九、|[\d]+\.)(.*?)\n/g, '$1$2\n\n');

    // 确保每个段落之间有空行
    formattedText = formattedText.replace(/\n([^\n])/g, '\n\n$1');

    // 确保段落开头有缩进
    const paragraphs = formattedText.split('\n\n');
    formattedText = paragraphs.map(p => {
        // 如果不是章节标题且不是空行，添加缩进
        if (p.trim() && !/(一、|二、|三、|四、|五、|六、|七、|八、|九、|[\d]+\.)/.test(p.trim().substring(0, 3))) {
            if (!p.startsWith('　　')) {
                return '　　' + p;
            }
        }
        return p;
    }).join('\n\n');

    return formattedText;
}

// 生成专利申请技术交底书
async function generatePatentDisclosure() {
    const projectName = projectNameInput.value.trim();
    if (!projectName) {
        showStatusMessage('请输入项目名称', 'error');
        return;
    }

    // 禁用生成按钮
    generateButton.disabled = true;

    // 显示加载指示器和进度条
    loadingIndicator.style.display = 'flex';
    progressContainer.style.display = 'block';
    simulateProgress();

    // 隐藏结果容器、思考容器和状态消息
    resultContainer.style.display = 'none';
    document.getElementById('thinkingContainer').style.display = 'none';
    statusMessage.style.display = 'none';

    // 清空结果和思考内容
    resultContent.innerHTML = '';
    document.getElementById('thinkingContent').innerHTML = '';

    // 重置滚动控制变量
    window.userScrolledThinking = false;
    window.userScrolledResult = false;

    try {
        // 显示状态消息
        showStatusMessage('正在生成专利申请技术交底书，这可能需要1-2分钟...', 'info');

        // 定义流式响应处理回调函数
        const onStreaming = (content, isThinking, isDone) => {
            if (isThinking) {
                // 显示思考过程
                const thinkingContainer = document.getElementById('thinkingContainer');
                const thinkingContent = document.getElementById('thinkingContent');

                thinkingContainer.style.display = 'block';
                thinkingContent.textContent = content;

                // 添加用户滚动检测变量，如果用户没有手动滚动，才自动滚动到底部
                if (!window.userScrolledThinking) {
                    thinkingContent.scrollTop = thinkingContent.scrollHeight;
                }
            } else {
                // 当开始显示结果时，隐藏思考过程框
                const thinkingContainer = document.getElementById('thinkingContainer');
                if (thinkingContainer) {
                    thinkingContainer.style.display = 'none';
                }

                // 显示结果
                resultContainer.style.display = 'block';

                // 格式化内容
                const formattedContent = formatPatentText(content);

                // 将格式化后的内容转换为HTML
                const htmlContent = convertToHtml(formattedContent);
                resultContent.innerHTML = htmlContent;

                // 添加用户滚动检测变量，如果用户没有手动滚动，才自动滚动到底部
                if (!window.userScrolledResult) {
                    resultContent.scrollTop = resultContent.scrollHeight;
                }

                // 如果已完成，启用按钮
                if (isDone) {
                    insertButton.disabled = false;
                    copyButton.disabled = false;

                    // 显示成功消息
                    showStatusMessage('专利申请技术交底书生成成功', 'success');

                    // 停止进度条动画
                    stopProgress();
                    progressBar.style.width = '100%';

                    // 隐藏加载指示器
                    loadingIndicator.style.display = 'none';
                }
            }
        };

        // 调用API生成专利申请技术交底书，传入回调函数
        await callPatentAPI(projectName, onStreaming);

    } catch (error) {
        console.error('生成专利申请技术交底书失败:', error);

        // 显示更友好的错误消息
        let errorMessage = '生成失败';
        if (error.message.includes('超时')) {
            errorMessage = '生成请求超时，服务器可能繁忙。请稍后再试。';
        } else if (error.message.includes('网络')) {
            errorMessage = '网络连接错误，请检查您的网络连接并重试。';
        } else {
            errorMessage = `生成失败: ${error.message}`;
        }

        showStatusMessage(errorMessage, 'error');

        // 停止进度条动画
        stopProgress();

        // 隐藏加载指示器和思考过程框
        loadingIndicator.style.display = 'none';

        // 隐藏思考过程框
        const thinkingContainer = document.getElementById('thinkingContainer');
        if (thinkingContainer) {
            thinkingContainer.style.display = 'none';
        }
    } finally {
        // 启用生成按钮
        generateButton.disabled = false;
    }
}

// 调用专利生成API
async function callPatentAPI(projectName) {
    try {
        // API配置 - 使用本地代理服务器转发到GPUStack API
        const API_URL = "/api/gpustack/patent";

        // 构建提示，包含专利申请技术交底书模板
        const prompt = `<think>我需要根据用户提供的项目名称，生成一份专业的专利申请技术交底书，而不是进行翻译或其他任务。</think>

请根据以下项目名称，生成一份完整的专利申请技术交底书。

项目名称：${projectName}

请按照以下结构生成专利申请技术交底书：

一、发明的名称。
能简单明了地反映发明的主题和类型，尽量表明发明对象的用途或者应用领域。不能使用非规范的技术语言和商标、代号、人名、地名等含义不清的词汇，字数限定在30个以内。名称一经确定,全部交底书使用均要一致。

二、所属技术领域。
指发明直接所属或直接应用的技术领域，如："本发明是一种电视机中使用的遥控器"；"本发明属于石油地质勘探钻机自动控制装置"；"本发明涉及低合金钢的热处理方法"等。

三、背景技术。
申请人所掌握的与本发明相关的同类技术现状（一般要以文献检索、资料为依据），应有针对性地与本发明对比，做如实描述和评价，必要时借助附图加以说明。具体内容包括：简要说明其结构和原理、工艺过程及条件；实事求是地说明背景技术存在的不足之处等。选好背景技术至关重要,它是发明的基础，关系到判断发明的价值，所以切忌主观臆断。最好能提供介绍背景技术的文献，并指出文献的"出处"，以及公知公用情况。凡涉及现有设备的要注明生产厂家、牌号；涉及专利的要给出专利号；经过检索的要附有检索报告。

四、发明的目的。
针对背景技术中存在的问题,正面明确地说明发明所要解决的技术问题,从而归纳出本发明的目的。切忌采用"节省能源"、"提高质量"、"克服上述技术中的缺点"等笼统提法。

五、发明的内容。
为实现上述目的,在本发明中采取的主要技术手段。要清楚、完整、准确地加以描述，要对发明的实质内容加以说明，公开的程度以所属技术领域的普通技术人员能够理解技和实现为准。
如：实用新型专利要描述产品是由哪些部件组成的，各部件间的位置关系和连接关系，其形状、构造有什么特征（注意：不要涉及产品的使用方法、功能）。如果是方法类发明，要写清必要的生产的条件及工艺步骤。与背景技术的不同点，要尽可能描述清楚,在描述每项技术手段时，应说明其在本发明中所起的作用，必要时应说明设计方案所依据的科学原理，以便专利代理师和审查员理解发明实质内容。

六、发明的效果。
与发明的目的、手段相对应，与背景技术相比具有的优点、特点和本发明所能达到的积极效果（最好有具体数据），具体地、实事求是地进行描述。

七、附图及附图的简单说明 。
发明人可提供描述本发明的必要的附图（电路图、结构图、框图及流程图等），用来帮助说明发明内容的。实用新型专利应至少包括一幅附图。附图应能清楚地体现发明内容，主要部件应顺序编号。在附图说明中要说明各视图的名称，图中标号所指示的零件、部件、部位名称。同一部件在不同视图中应用同一标号。
交底书中未提及的附图标号，图中不应出现，交底书中提及的零部件，图中均应加以标注。各种图要使用黑色墨笔画（不要用铅笔画），图幅A4幅面大小，图的大小及清晰度应保证在该图缩小到三分之二时仍能清楚地分辨出图中各个细节。可采用多种绘图方式，最好按制图标准制作，附图可不按比例，图中不要出现汉字(必要时要打印)、尺寸线、尺寸。涉及电路的一定要有方框图或电路图。

八、实施例。
列举实现发明内容的实例，是以上第五部分的扩展。即各具体技术方案的构成、最佳设计。可结合附图说明发明的组份、流程、形状、构造，为了使发明更容易理解，必要时可说明功能、动态构造和使用方法（但不要写成使用说明书）等。
注意：这里的"实施"不是该发明在实践中如何应用，取得了什么收益，"应用"和"收益"对专利来说是"效果"。应写在以上发明的效果部分。
对方法发明，工艺条件可以用不同的参数选择表示不同的实施方案；对产品发明，不同的实施方案是指几种具有同一构思的具体结构、配方和组份。必要时可列举多个实施例,每个实施例都必须与整体技术方案的目的和效果相一致。写好实施例,可增加该发明的可实施性,提高该发明的份量,交底的原则是宁多勿少、宁细勿粗、宁实勿虚。

请根据以上模板和项目名称，生成一份完整、专业的专利申请技术交底书。内容要详实、专业，符合专利申请的要求。请直接给出最终结果，不要包含思考过程。请使用标准的段落格式，每个章节之间用空行分隔，不要使用Markdown标记如#、##、*等。请确保每个章节标题使用“一、”“二、”等格式，并且每个章节标题单独成行。

注意：这是一个专利申请技术交底书生成任务，不是翻译任务。请直接用中文生成专利申请技术交底书，不要将内容翻译成英文或其他语言。</think>`;

        // 设置API参数
        const temperature = 0.7;
        const top_p = 0.9;
        const frequency_penalty = 0;
        const presence_penalty = 0;

        // 准备请求体 - 使用GPUStack API的格式
        const requestBody = {
            messages: [
                {
                    role: "user",
                    content: prompt
                }
            ],
            model: "deepseek-r1",
            stream: true, // 启用流式输出
            temperature: temperature,
            top_p: top_p,
            frequency_penalty: frequency_penalty,
            presence_penalty: presence_penalty,
            max_tokens: 4096
        };

        console.log('发送API请求:', API_URL);
        console.log('请求参数:', JSON.stringify(requestBody, null, 2));

        // 添加回调函数参数，用于处理流式响应
        const onStreaming = typeof arguments[1] === 'function' ? arguments[1] : null;

        // 添加重试机制
        let maxRetries = 2;
        let retryCount = 0;
        let lastError = null;

        while (retryCount <= maxRetries) {
            try {
                console.log(`尝试调用GPUStack API (尝试 ${retryCount + 1}/${maxRetries + 1})...`);

                // 发送请求
                const response = await fetch(API_URL, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    throw new Error(`API请求失败: ${response.status}`);
                }

                // 处理流式响应
                if (requestBody.stream) {
                    console.log('处理流式响应...');

                    // 创建读取器和解码器
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder('utf-8', { fatal: false });

                    // 用于存储完整响应的变量
                    let fullContent = '';
                    let buffer = ''; // 用于存储不完整的数据行

                    // 读取流式响应
                    while (true) {
                        const { done, value } = await reader.read();

                        if (done) {
                            console.log('流式响应读取完成');
                            break;
                        }

                        // 解码二进制数据
                        let chunk;
                        try {
                            chunk = decoder.decode(value, { stream: true });
                        } catch (e) {
                            console.error('解码数据块时出错:', e);
                            chunk = new TextDecoder('utf-8', { fatal: false }).decode(value);
                        }

                        // 处理数据块
                        buffer += chunk;

                        // 按行分割数据
                        const lines = buffer.split('\n');
                        buffer = lines.pop() || ''; // 最后一行可能不完整，保存到buffer中

                        // 处理每一行数据
                        for (const line of lines) {
                            // 跳过空行
                            if (!line.trim()) continue;

                            // 检查是否是SSE格式的数据行
                            if (line.startsWith('data: ')) {
                                const data = line.substring(6);

                                // 检查是否是结束标记
                                if (data === '[DONE]') {
                                    console.log('收到流式响应结束标记');
                                    continue;
                                }

                                try {
                                    // 解析JSON数据
                                    const json = JSON.parse(data);

                                    // 检查是否包含delta内容
                                    if (json.choices && json.choices.length > 0 && json.choices[0].delta) {
                                        const delta = json.choices[0].delta;

                                        // 提取内容
                                        if (delta.content) {
                                            const content = delta.content;
                                            fullContent += content;

                                            // 查找最后一个</think>标签的位置
                                            const thinkEndIndex = fullContent.lastIndexOf('</think>');

                                            if (thinkEndIndex !== -1) {
                                                // 找到了</think>标签，只输出它之后的内容
                                                const displayContent = fullContent.substring(thinkEndIndex + 8);

                                                // 只有当有内容可显示时才更新
                                                if (displayContent && onStreaming) {
                                                    onStreaming(displayContent, false, false);
                                                }
                                            } else {
                                                // 没有找到</think>标签，说明思考过程尚未结束
                                                // 可以选择显示思考过程或等待完整结果
                                                if (onStreaming) {
                                                    onStreaming(fullContent, true, false);
                                                }
                                            }
                                        }
                                    }
                                } catch (e) {
                                    console.error('解析数据行失败:', e, line);
                                }
                            }
                        }
                    }

                    // 处理最终结果
                    console.log('流式响应处理完成，提取最终结果');

                    // 查找最后一个</think>标签的位置
                    const thinkEndIndex = fullContent.lastIndexOf('</think>');
                    let finalResult;

                    if (thinkEndIndex !== -1) {
                        // 找到了</think>标签，只使用它之后的内容作为最终结果
                        finalResult = fullContent.substring(thinkEndIndex + 8);
                        console.log('最终结果提取完成，长度:', finalResult.length);
                    } else {
                        // 如果没有找到</think>标签，则使用完整内容
                        console.log('未找到</think>标签，使用完整内容作为结果');
                        finalResult = fullContent;
                    }

                    // 通知流式处理已完成
                    if (onStreaming) {
                        onStreaming(finalResult, false, true);
                    }

                    return finalResult;
                } else {
                    // 非流式响应处理
                    const data = await response.json();
                    console.log('API返回数据:', data);

                    // 提取响应文本 - GPUStack API返回格式与OpenAI兼容
                    if (data.choices && data.choices.length > 0 && data.choices[0].message) {
                        return data.choices[0].message.content;
                    } else {
                        throw new Error('API返回数据格式错误');
                    }
                }
            } catch (error) {
                lastError = error;
                retryCount++;
                console.error(`API调用失败 (尝试 ${retryCount}/${maxRetries + 1}):`, error.message);

                // 如果已经达到最大重试次数，抛出错误
                if (retryCount > maxRetries) {
                    break;
                }

                // 显示重试状态
                showStatusMessage(`API请求失败，正在重试 (${retryCount}/${maxRetries})...`, 'warning');

                // 等待一段时间后重试
                const retryDelay = 3000 * retryCount; // 递增延迟
                console.log(`将在 ${retryDelay}ms 后重试...`);
                await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
        }

        // 如果所有重试都失败，抛出最后一个错误
        if (lastError) {
            // 提供更友好的错误消息
            let errorMessage = '无法连接到生成服务';
            if (lastError.message.includes('超时')) {
                errorMessage = '生成服务响应超时，请稍后再试';
            } else if (lastError.message.includes('网络')) {
                errorMessage = '网络连接错误，请检查您的网络连接';
            } else {
                errorMessage = `生成服务错误: ${lastError.message}`;
            }
            throw new Error(errorMessage);
        } else {
            throw new Error('未知错误，无法连接到生成服务');
        }
    } catch (error) {
        console.error('API调用失败:', error);
        // 直接抛出错误，保留完整的错误信息
        throw error;
    }
}

// 插入到文档
function insertToDocument() {
    try {
        // 获取内容，使用innerHTML来获取HTML格式
        const htmlContent = resultContent.innerHTML;
        if (!htmlContent) {
            showStatusMessage('没有可插入的内容', 'error');
            return;
        }

        // 禁用插入按钮，防止重复操作
        insertButton.disabled = true;

        // 获取WPS应用程序对象
        const app = window.Application;
        if (!app || !app.ActiveDocument) {
            showStatusMessage('无法访问当前文档，请确保已打开文档', 'error');
            insertButton.disabled = false;
            return;
        }

        // 获取当前文档的选择区域
        const selection = app.Selection;

        // 使用更可靠的方法提取内容
        // 首先，找出所有章节标题的位置
        const titleRegex = /<h3[^>]*>(一、|二、|三、|四、|五、|六、|七、|八、|九、)(.*?)<\/h3>/g;
        const titles = [];
        let titleMatch;

        while ((titleMatch = titleRegex.exec(htmlContent)) !== null) {
            titles.push({
                fullTitle: titleMatch[0],
                titleNumber: titleMatch[1],
                titleContent: titleMatch[2],
                position: titleMatch.index
            });
        }

        // 按位置排序标题
        titles.sort((a, b) => a.position - b.position);

        // 对于每个标题，找出它和下一个标题之间的所有段落
        for (let i = 0; i < titles.length; i++) {
            const currentTitle = titles[i];
            const nextTitle = (i < titles.length - 1) ? titles[i + 1] : null;

            // 插入当前标题
            selection.Font.Bold = true;
            selection.TypeText(currentTitle.titleNumber + currentTitle.titleContent);
            selection.Font.Bold = false;
            selection.TypeParagraph(); // 标题后只加一个空行

            // 找出当前标题和下一个标题之间的所有段落
            let startPos = htmlContent.indexOf('</h3>', currentTitle.position) + 5;
            let endPos = nextTitle ? nextTitle.position : htmlContent.length;
            let sectionHtml = htmlContent.substring(startPos, endPos);

            // 提取段落
            const paragraphRegex = /<p[^>]*>(.*?)<\/p>/g;
            let paragraphMatch;

            while ((paragraphMatch = paragraphRegex.exec(sectionHtml)) !== null) {
                let paraContent = paragraphMatch[1]
                    .replace(/&emsp;/g, '　') // 替换HTML空格实体
                    .replace(/<br\s*\/?>/g, '\n　　'); // 处理内部换行

                // 确保段落开头有缩进
                if (!paraContent.startsWith('　　')) {
                    paraContent = '　　' + paraContent;
                }

                // 确保段落内容不加粗
                selection.Font.Bold = false;
                selection.TypeText(paraContent);
                selection.TypeParagraph(); // 段落后只加一个空行
            }
        }

        showStatusMessage('内容已成功插入到文档', 'success');

        // 操作完成后重新启用按钮
        insertButton.disabled = false;
    } catch (error) {
        console.error('插入文档失败:', error);
        showStatusMessage(`插入失败: ${error.message}`, 'error');
        insertButton.disabled = false;
    }
}

// 复制内容到剪贴板
function copyContent() {
    // 检查是否有内容可复制
    if (!resultContent.innerHTML) {
        showStatusMessage('没有可复制的内容', 'error');
        return;
    }

    try {
        // 创建一个隐藏的div来保存格式化的内容
        const tempDiv = document.createElement('div');
        tempDiv.style.position = 'fixed';
        tempDiv.style.left = '-999999px';
        tempDiv.style.top = '-999999px';
        document.body.appendChild(tempDiv);

        // 创建一个Word兼容的HTML内容
        const htmlContent = createWordCompatibleHtml();
        tempDiv.innerHTML = htmlContent;

        // 创建一个范围并选择内容
        const range = document.createRange();
        range.selectNodeContents(tempDiv);

        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(range);

        // 尝试复制HTML内容
        const successful = document.execCommand('copy');
        document.body.removeChild(tempDiv);

        // 清除选择
        selection.removeAllRanges();

        if (successful) {
            showStatusMessage('内容已复制到剪贴板', 'success');
            setTimeout(() => {
                statusMessage.style.display = 'none';
            }, 2000);
            return;
        }
    } catch (err) {
        console.error('HTML复制方法失败:', err);
    }

    // 如果HTML复制失败，尝试使用WPS的API
    try {
        if (window.Application && window.Application.System) {
            // 尝试使用WPS API直接操作文档
            try {
                // 创建一个临时文档来处理复制操作
                const app = window.Application;

                // 尝试使用更直接的方法：直接将内容写入剪贴板
                // 这种方法模拟了"插入文档"功能的操作

                // 获取HTML内容并解析
                const htmlContent = resultContent.innerHTML;

                // 提取所有章节标题
                const titleRegex = /<h3[^>]*>(一、|二、|三、|四、|五、|六、|七、|八、|九、)(.*?)<\/h3>/g;
                const titles = [];
                let titleMatch;

                while ((titleMatch = titleRegex.exec(htmlContent)) !== null) {
                    titles.push({
                        titleNumber: titleMatch[1],
                        titleContent: titleMatch[2],
                        position: titleMatch.index
                    });
                }

                // 按位置排序标题
                titles.sort((a, b) => a.position - b.position);

                // 创建一个临时文档
                const tempDoc = app.Documents.Add();
                const selection = tempDoc.Range();

                // 对于每个标题，找出它和下一个标题之间的所有段落
                for (let i = 0; i < titles.length; i++) {
                    const currentTitle = titles[i];
                    const nextTitle = (i < titles.length - 1) ? titles[i + 1] : null;

                    // 插入当前标题（加粗）
                    selection.Font.Bold = true;
                    selection.TypeText(currentTitle.titleNumber + currentTitle.titleContent);
                    selection.Font.Bold = false;
                    selection.TypeParagraph(); // 标题后加一个空行

                    // 找出当前标题和下一个标题之间的所有段落
                    let startPos = htmlContent.indexOf('</h3>', currentTitle.position) + 5;
                    let endPos = nextTitle ? nextTitle.position : htmlContent.length;
                    let sectionHtml = htmlContent.substring(startPos, endPos);

                    // 提取段落
                    const paragraphRegex = /<p[^>]*>(.*?)<\/p>/g;
                    let paragraphMatch;

                    while ((paragraphMatch = paragraphRegex.exec(sectionHtml)) !== null) {
                        let paraContent = paragraphMatch[1]
                            .replace(/&emsp;/g, '　') // 替换HTML空格实体
                            .replace(/<br\s*\/?>/g, '\n　　'); // 处理内部换行

                        // 确保段落开头有缩进
                        if (!paraContent.startsWith('　　')) {
                            paraContent = '　　' + paraContent;
                        }

                        // 插入段落（不加粗）
                        selection.Font.Bold = false;
                        selection.TypeText(paraContent);
                        selection.TypeParagraph(); // 段落后加一个空行
                    }
                }

                // 全选文档内容
                selection.WholeStory();

                // 复制到剪贴板
                selection.Copy();

                // 关闭临时文档（不保存）
                tempDoc.Close(false);

                showStatusMessage('内容已复制到剪贴板（与插入文档格式相同）', 'success');
                setTimeout(() => {
                    statusMessage.style.display = 'none';
                }, 2000);
                return;
            } catch (docErr) {
                console.error('WPS文档操作失败，尝试HTML复制:', docErr);

                // 如果文档操作失败，尝试HTML复制
                try {
                    const htmlContent = createWordCompatibleHtml();
                    window.Application.System.ClipboardHTML = htmlContent;
                    showStatusMessage('内容已复制到剪贴板', 'success');
                    setTimeout(() => {
                        statusMessage.style.display = 'none';
                    }, 2000);
                    return;
                } catch (htmlErr) {
                    console.error('WPS HTML复制失败，尝试纯文本:', htmlErr);
                    // 如果HTML复制失败，回退到纯文本
                    window.Application.System.ClipboardText = resultContent.textContent;
                    showStatusMessage('内容已复制到剪贴板（纯文本格式）', 'success');
                    setTimeout(() => {
                        statusMessage.style.display = 'none';
                    }, 2000);
                    return;
                }
            }
        }
    } catch (wpsErr) {
        console.error('WPS复制方法失败:', wpsErr);
    }

    // 如果前两种方法失败，尝试使用现代剪贴板API
    // 注意：现代剪贴板API通常只支持纯文本或特定格式
    try {
        if (navigator.clipboard) {
            // 尝试使用Clipboard API的writeText方法（纯文本）
            navigator.clipboard.writeText(resultContent.textContent)
                .then(() => {
                    showStatusMessage('内容已复制到剪贴板（纯文本格式）', 'warning');
                    setTimeout(() => {
                        statusMessage.style.display = 'none';
                    }, 2000);
                })
                .catch(clipErr => {
                    console.error('剪贴板API复制失败:', clipErr);
                    showFormattedCopyDialog();
                });
            return;
        }
    } catch (clipboardErr) {
        console.error('现代剪贴板API失败:', clipboardErr);
    }

    // 如果所有自动方法都失败，显示格式化复制对话框
    showFormattedCopyDialog();
}

// 创建Word兼容的HTML内容
function createWordCompatibleHtml() {
    // 使用与"插入文档"功能相同的方法处理内容
    // 这样可以确保复制的内容与插入的内容格式完全一致

    // 获取当前的HTML内容
    const htmlContent = resultContent.innerHTML;

    // 创建一个临时div来构建纯文本内容
    const tempDiv = document.createElement('div');
    tempDiv.style.position = 'absolute';
    tempDiv.style.left = '-9999px';
    tempDiv.style.top = '-9999px';
    document.body.appendChild(tempDiv);

    // 提取所有章节标题
    const titleRegex = /<h3[^>]*>(一、|二、|三、|四、|五、|六、|七、|八、|九、)(.*?)<\/h3>/g;
    const titles = [];
    let titleMatch;

    while ((titleMatch = titleRegex.exec(htmlContent)) !== null) {
        titles.push({
            fullTitle: titleMatch[0],
            titleNumber: titleMatch[1],
            titleContent: titleMatch[2],
            position: titleMatch.index
        });
    }

    // 按位置排序标题
    titles.sort((a, b) => a.position - b.position);

    // 构建RTF格式的文本内容
    let rtfContent = '';

    // 对于每个标题，找出它和下一个标题之间的所有段落
    for (let i = 0; i < titles.length; i++) {
        const currentTitle = titles[i];
        const nextTitle = (i < titles.length - 1) ? titles[i + 1] : null;

        // 添加标题（加粗）
        rtfContent += `<p><b>${currentTitle.titleNumber}${currentTitle.titleContent}</b></p>\n`;

        // 找出当前标题和下一个标题之间的所有段落
        let startPos = htmlContent.indexOf('</h3>', currentTitle.position) + 5;
        let endPos = nextTitle ? nextTitle.position : htmlContent.length;
        let sectionHtml = htmlContent.substring(startPos, endPos);

        // 提取段落
        const paragraphRegex = /<p[^>]*>(.*?)<\/p>/g;
        let paragraphMatch;

        while ((paragraphMatch = paragraphRegex.exec(sectionHtml)) !== null) {
            let paraContent = paragraphMatch[1]
                .replace(/&emsp;/g, '　') // 替换HTML空格实体
                .replace(/<br\s*\/?>/g, '\n　　'); // 处理内部换行

            // 确保段落开头有缩进
            if (!paraContent.startsWith('　　')) {
                paraContent = '　　' + paraContent;
            }

            // 添加段落（不加粗）
            rtfContent += `<p>${paraContent}</p>\n`;
        }
    }

    // 创建一个包含Word兼容格式的HTML文档
    const wordHtml = `
    <html xmlns:o="urn:schemas-microsoft-com:office:office"
          xmlns:w="urn:schemas-microsoft-com:office:word"
          xmlns:v="urn:schemas-microsoft-com:vml"
          xmlns="http://www.w3.org/TR/REC-html40">
    <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="ProgId" content="Word.Document">
    <meta name="Generator" content="Microsoft Word 15">
    <meta name="Originator" content="Microsoft Word 15">
    <style>
    /* 基本样式 */
    body {
        font-family: SimSun, 宋体, serif;
        font-size: 12pt;
        line-height: 1.5;
        background-color: transparent;
        background: none;
    }
    p {
        margin: 0;
        padding: 0;
        margin-bottom: 10px;
        background-color: transparent;
        background: none;
    }
    b {
        font-weight: bold;
    }
    * {
        background-color: transparent !important;
        background: none !important;
    }
    </style>
    </head>
    <body>
    ${rtfContent}
    </body>
    </html>`;

    // 清理临时div
    document.body.removeChild(tempDiv);

    return wordHtml;
}

// 显示格式化复制对话框
function showFormattedCopyDialog() {
    // 创建一个对话框容器
    const dialogDiv = document.createElement('div');
    dialogDiv.style.position = 'fixed';
    dialogDiv.style.top = '50%';
    dialogDiv.style.left = '50%';
    dialogDiv.style.transform = 'translate(-50%, -50%)';
    dialogDiv.style.backgroundColor = 'white';
    dialogDiv.style.padding = '20px';
    dialogDiv.style.borderRadius = '8px';
    dialogDiv.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
    dialogDiv.style.zIndex = '1000';
    dialogDiv.style.width = '80%';
    dialogDiv.style.maxWidth = '600px';

    // 添加标题
    const titleDiv = document.createElement('div');
    titleDiv.textContent = '请手动复制以下内容';
    titleDiv.style.fontSize = '18px';
    titleDiv.style.fontWeight = 'bold';
    titleDiv.style.marginBottom = '15px';
    titleDiv.style.textAlign = 'center';
    dialogDiv.appendChild(titleDiv);

    // 添加提示
    const instructionDiv = document.createElement('div');
    instructionDiv.innerHTML = '<strong>保留格式复制方法（与插入文档效果相同）：</strong><br>1. 点击下方"复制格式文本"按钮<br>2. 在Word中使用Ctrl+V粘贴<br><br><strong>或者使用纯文本复制：</strong><br>1. 点击下方"复制纯文本"按钮<br>2. 在Word中使用Ctrl+V粘贴（注意：将丢失格式）';
    instructionDiv.style.marginBottom = '15px';
    instructionDiv.style.color = '#333';
    instructionDiv.style.backgroundColor = '#f5f5f5';
    instructionDiv.style.padding = '10px';
    instructionDiv.style.borderRadius = '4px';
    dialogDiv.appendChild(instructionDiv);

    // 创建按钮容器
    const buttonContainer = document.createElement('div');
    buttonContainer.style.display = 'flex';
    buttonContainer.style.justifyContent = 'space-around';
    buttonContainer.style.marginBottom = '15px';
    dialogDiv.appendChild(buttonContainer);

    // 创建复制格式文本按钮
    const copyHtmlButton = document.createElement('button');
    copyHtmlButton.textContent = '复制格式文本';
    copyHtmlButton.style.padding = '8px 15px';
    copyHtmlButton.style.backgroundColor = '#4CAF50';
    copyHtmlButton.style.color = 'white';
    copyHtmlButton.style.border = 'none';
    copyHtmlButton.style.borderRadius = '4px';
    copyHtmlButton.style.cursor = 'pointer';
    buttonContainer.appendChild(copyHtmlButton);

    // 创建复制纯文本按钮
    const copyTextButton = document.createElement('button');
    copyTextButton.textContent = '复制纯文本';
    copyTextButton.style.padding = '8px 15px';
    copyTextButton.style.backgroundColor = '#2196F3';
    copyTextButton.style.color = 'white';
    copyTextButton.style.border = 'none';
    copyTextButton.style.borderRadius = '4px';
    copyTextButton.style.cursor = 'pointer';
    buttonContainer.appendChild(copyTextButton);

    // 创建一个隐藏的div来存储HTML内容
    const htmlDiv = document.createElement('div');
    htmlDiv.style.display = 'none';
    htmlDiv.innerHTML = createWordCompatibleHtml();
    dialogDiv.appendChild(htmlDiv);

    // 创建一个隐藏的textarea来存储纯文本内容
    const textArea = document.createElement('textarea');
    textArea.value = resultContent.textContent;
    textArea.style.display = 'none';
    dialogDiv.appendChild(textArea);

    // 添加预览区域
    const previewDiv = document.createElement('div');
    previewDiv.style.border = '1px solid #ccc';
    previewDiv.style.borderRadius = '4px';
    previewDiv.style.padding = '10px';
    previewDiv.style.height = '200px';
    previewDiv.style.overflow = 'auto';
    previewDiv.style.backgroundColor = '#fff';

    // 创建一个干净的预览内容，确保没有背景色
    const cleanPreviewContent = document.createElement('div');
    cleanPreviewContent.innerHTML = resultContent.innerHTML;

    // 清理预览内容中的背景样式
    const previewH3s = cleanPreviewContent.querySelectorAll('h3');
    previewH3s.forEach(h3 => {
        h3.style.backgroundColor = 'transparent';
        h3.style.background = 'none';
    });

    const previewPs = cleanPreviewContent.querySelectorAll('p');
    previewPs.forEach(p => {
        p.style.backgroundColor = 'transparent';
        p.style.background = 'none';
    });

    previewDiv.innerHTML = cleanPreviewContent.innerHTML;
    dialogDiv.appendChild(previewDiv);

    // 添加关闭按钮
    const closeButton = document.createElement('button');
    closeButton.textContent = '关闭';
    closeButton.style.padding = '8px 15px';
    closeButton.style.backgroundColor = '#f44336';
    closeButton.style.color = 'white';
    closeButton.style.border = 'none';
    closeButton.style.borderRadius = '4px';
    closeButton.style.cursor = 'pointer';
    closeButton.style.display = 'block';
    closeButton.style.margin = '15px auto 0';
    dialogDiv.appendChild(closeButton);

    // 复制HTML按钮点击事件
    copyHtmlButton.onclick = function() {
        try {
            // 创建一个范围并选择HTML内容
            const range = document.createRange();
            range.selectNodeContents(htmlDiv);

            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);

            // 执行复制命令
            const successful = document.execCommand('copy');

            // 清除选择
            selection.removeAllRanges();

            if (successful) {
                copyHtmlButton.textContent = '✓ 已复制格式文本';
                copyHtmlButton.style.backgroundColor = '#45a049';
                setTimeout(() => {
                    copyHtmlButton.textContent = '复制格式文本';
                    copyHtmlButton.style.backgroundColor = '#4CAF50';
                }, 2000);
            } else {
                copyHtmlButton.textContent = '× 复制失败';
                copyHtmlButton.style.backgroundColor = '#e53935';
            }
        } catch (err) {
            console.error('复制HTML失败:', err);
            copyHtmlButton.textContent = '× 复制失败';
            copyHtmlButton.style.backgroundColor = '#e53935';
        }
    };

    // 复制纯文本按钮点击事件
    copyTextButton.onclick = function() {
        try {
            // 选择文本区域内容
            textArea.style.display = 'block';
            textArea.select();

            // 执行复制命令
            const successful = document.execCommand('copy');

            // 隐藏文本区域
            textArea.style.display = 'none';

            if (successful) {
                copyTextButton.textContent = '✓ 已复制纯文本';
                copyTextButton.style.backgroundColor = '#1976D2';
                setTimeout(() => {
                    copyTextButton.textContent = '复制纯文本';
                    copyTextButton.style.backgroundColor = '#2196F3';
                }, 2000);
            } else {
                copyTextButton.textContent = '× 复制失败';
                copyTextButton.style.backgroundColor = '#e53935';
            }
        } catch (err) {
            console.error('复制纯文本失败:', err);
            copyTextButton.textContent = '× 复制失败';
            copyTextButton.style.backgroundColor = '#e53935';
        }
    };

    // 关闭按钮点击事件
    closeButton.onclick = function() {
        document.body.removeChild(dialogDiv);
    };

    // 添加到文档
    document.body.appendChild(dialogDiv);

    showStatusMessage('请从弹出窗口中选择复制方式', 'warning');
}

// 显示手动复制对话框（保留作为备用方法）
function showManualCopyDialog(content) {
    // 创建一个对话框容器
    const dialogDiv = document.createElement('div');
    dialogDiv.style.position = 'fixed';
    dialogDiv.style.top = '50%';
    dialogDiv.style.left = '50%';
    dialogDiv.style.transform = 'translate(-50%, -50%)';
    dialogDiv.style.backgroundColor = 'white';
    dialogDiv.style.padding = '20px';
    dialogDiv.style.borderRadius = '8px';
    dialogDiv.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
    dialogDiv.style.zIndex = '1000';
    dialogDiv.style.width = '80%';
    dialogDiv.style.maxWidth = '600px';

    // 添加标题
    const titleDiv = document.createElement('div');
    titleDiv.textContent = '请手动复制以下内容';
    titleDiv.style.fontSize = '18px';
    titleDiv.style.fontWeight = 'bold';
    titleDiv.style.marginBottom = '15px';
    titleDiv.style.textAlign = 'center';
    dialogDiv.appendChild(titleDiv);

    // 添加提示
    const instructionDiv = document.createElement('div');
    instructionDiv.textContent = '请先选中文本（Ctrl+A），然后复制（Ctrl+C）';
    instructionDiv.style.marginBottom = '10px';
    instructionDiv.style.color = '#666';
    dialogDiv.appendChild(instructionDiv);

    // 创建一个可见的文本区域，让用户手动复制
    const textArea = document.createElement('textarea');
    textArea.value = content;
    textArea.style.width = '100%';
    textArea.style.height = '200px';
    textArea.style.padding = '10px';
    textArea.style.border = '1px solid #ccc';
    textArea.style.borderRadius = '4px';
    textArea.style.resize = 'none';
    dialogDiv.appendChild(textArea);

    // 添加关闭按钮
    const closeButton = document.createElement('button');
    closeButton.textContent = '关闭';
    closeButton.style.padding = '8px 15px';
    closeButton.style.backgroundColor = '#1e88e5';
    closeButton.style.color = 'white';
    closeButton.style.border = 'none';
    closeButton.style.borderRadius = '4px';
    closeButton.style.cursor = 'pointer';
    closeButton.style.display = 'block';
    closeButton.style.margin = '15px auto 0';

    closeButton.onclick = function() {
        document.body.removeChild(dialogDiv);
    };

    dialogDiv.appendChild(closeButton);

    // 添加到文档
    document.body.appendChild(dialogDiv);

    // 自动选中文本
    textArea.focus();
    textArea.select();

    showStatusMessage('自动复制失败，请从弹出窗口中手动复制内容', 'error');
}



// 显示状态消息
function showStatusMessage(message, type) {
    statusMessage.textContent = message;
    statusMessage.className = `status-message status-${type}`;
    statusMessage.style.display = 'block';

    // 3秒后自动隐藏
    setTimeout(() => {
        statusMessage.style.display = 'none';
    }, 3000);
}

// 模拟进度条
function simulateProgress() {
    let width = 0;
    progressBar.style.width = '0%';

    progressInterval = setInterval(() => {
        if (width >= 90) {
            // 到90%后停止，等待实际完成
            clearInterval(progressInterval);
            return;
        }

        // 进度增加速度随进度增加而减慢
        const increment = Math.max(0.5, 5 * (1 - width / 100));
        width += increment;
        progressBar.style.width = width + '%';
    }, 100);
}

// 停止进度条动画
function stopProgress() {
    clearInterval(progressInterval);
    progressBar.style.width = '100%';

    // 完成后短暂延迟隐藏进度条
    setTimeout(() => {
        progressContainer.style.display = 'none';
        progressBar.style.width = '0%';
    }, 300);
}

// 页面加载完成后初始化
window.addEventListener('DOMContentLoaded', init);
