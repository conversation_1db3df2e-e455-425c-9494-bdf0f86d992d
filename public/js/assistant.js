// 声明全局变量
let chatMessages;
let messageInput;
let sendButton;
let loadingIndicator;
let progressContainer;
let progressBar;

// 消息历史记录
let messageHistory = [
    // 添加系统消息，告诉模型它的角色
    {
        role: "system",
        content: "你是一个智能助手，基于DeepSeek大模型运行。你的任务是提供有用、准确、安全的信息和帮助。回答要简洁明确，如果不知道答案，请说明你不知道而不是提供错误信息。"
    },
    // 添加助手的欢迎消息
    {
        role: "assistant",
        content: "您好，我是基于DeepSeek大模型的智能助手。我可以回答您的问题、提供信息和协助您完成各种任务。请问有什么可以帮助您的？"
    }
];

// 初始化函数
function init() {
    console.log('初始化智能助手...');

    // 获取DOM元素
    chatMessages = document.getElementById('chatMessages');
    messageInput = document.getElementById('messageInput');
    sendButton = document.getElementById('sendButton');
    loadingIndicator = document.getElementById('loadingIndicator');
    progressContainer = document.getElementById('progressContainer');
    progressBar = document.getElementById('progressBar');

    // 检查DOM元素是否存在
    if (!chatMessages || !messageInput || !sendButton || !loadingIndicator || !progressContainer || !progressBar) {
        console.error('无法获取必要的DOM元素，请检查HTML结构');
        return;
    }

    console.log('DOM元素已获取');

    // 智能助手不需要自动获取选中的文本
    // 直接添加连接状态检测
    checkServerConnection();

    // 清空输入框，确保没有预填充的文本
    messageInput.value = '';

    // 设置事件监听器
    sendButton.addEventListener('click', sendMessage);
    messageInput.addEventListener('keydown', function(e) {
        // 按下Enter键发送消息，按下Shift+Enter换行
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // 添加测试连接按钮事件
    const testConnectionBtn = document.getElementById('testConnectionBtn');
    if (testConnectionBtn) {
        testConnectionBtn.addEventListener('click', testAPIConnection);
        console.log('测试连接按钮事件已添加');
    } else {
        console.warn('未找到测试连接按钮');
    }

    // 自动调整文本区域高度
    messageInput.addEventListener('input', autoResizeTextarea);

    console.log('智能助手初始化完成');
}

// 测试API连接函数
async function testAPIConnection() {
    try {
        // 更新状态指示器
        updateServerStatus('checking');
        console.log('正在测试与GPUStack API的连接...');

        // 测试简单问题 - 使用最简短的测试内容
        const testQuestion = "你好";

        // 设置测试开始时间
        const startTime = new Date();

        try {
            // 使用本地代理服务器转发到GPUStack API
            const API_URL = "/api/gpustack/assistant";
            console.log('测试连接到GPUStack API端点:', API_URL);

            // 设置超时
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

            const response = await fetch(API_URL, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    messages: [
                        {
                            role: "user",
                            content: testQuestion
                        }
                    ],
                    model: "deepseek-r1"
                }),
                signal: controller.signal
            });

            // 清除超时
            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`GPUStack API请求失败: ${response.status} - ${response.statusText}`);
            }

            const data = await response.json();
            console.log('GPUStack API测试响应:', data);

            // 提取响应文本
            let responseText = "";
            if (data.choices && data.choices.length > 0 && data.choices[0].message) {
                responseText = data.choices[0].message.content;
            } else {
                responseText = JSON.stringify(data);
            }

            // 计算响应时间
            const endTime = new Date();
            const responseTime = endTime - startTime;

            // 只更新状态指示器，不添加消息
            if (responseText && responseText.length > 0 && !responseText.includes('无法连接') && !responseText.includes('错误')) {
                // 更新状态指示器
                updateServerStatus('connected', `响应时间: ${responseTime}ms`);
                console.log(`GPUStack API连接测试成功！响应时间: ${responseTime}ms`);
            } else {
                // 更新状态指示器
                updateServerStatus('error', '无效响应');
                console.log(`GPUStack API连接测试失败。服务器响应: ${responseText}`);
            }
        } catch (apiError) {
            console.error('调用GPUStack API失败:', apiError);

            // 直接更新状态为错误
            updateServerStatus('error', 'GPUStack API连接失败');
            console.log("警告：无法连接到GPUStack API。您可能无法使用智能助手功能。");
        }
    } catch (error) {
        console.error('测试连接失败:', error);
        updateServerStatus('error', error.message);
        console.log(`连接测试失败: ${error.message}`);
    }
}

// 更新服务器状态指示器
function updateServerStatus(status, message) {
    const statusIndicator = document.getElementById('statusIndicator');
    const statusText = document.getElementById('statusText');

    if (statusIndicator && statusText) {
        // 设置状态颜色
        switch(status) {
            case 'checking':
                statusIndicator.style.backgroundColor = '#999'; // 灰色
                statusText.textContent = '检测中...';
                break;
            case 'connected':
                statusIndicator.style.backgroundColor = '#4CAF50'; // 绿色
                statusText.textContent = '已连接';
                break;
            case 'error':
                statusIndicator.style.backgroundColor = '#F44336'; // 红色
                statusText.textContent = '连接失败';
                break;
            default:
                statusIndicator.style.backgroundColor = '#999';
                statusText.textContent = '未知状态';
        }

        // 如果有额外消息，显示在状态文本中
        if (message) {
            statusText.textContent += ': ' + message;
        }
    }
}

// 检查服务器连接状态
async function checkServerConnection() {
    try {
        // 更新状态指示器
        updateServerStatus('checking');
        console.log('正在检查与GPUStack API的连接...');

        // 检查主要API端点
        const API_URL = "/api/gpustack/assistant";
        console.log('测试连接到GPUStack API端点:', API_URL);

        // 设置超时
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

        try {
            // 发送简单的POST请求测试连接
            const response = await fetch(API_URL, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    messages: [
                        {
                            role: "user",
                            content: "你好"
                        }
                    ],
                    model: "deepseek-r1"
                }),
                signal: controller.signal
            }).catch(e => {
                console.error('GPUStack API连接测试失败:', e);
                throw e;
            });

            // 清除超时
            clearTimeout(timeoutId);

            if (response.ok) {
                console.log('GPUStack API连接成功');
                // 更新状态指示器
                updateServerStatus('connected');

                // 初始化时添加欢迎消息
                if (chatMessages.children.length === 0) {
                    addMessage("您好，我是基于DeepSeek大模型的智能助手。我可以回答您的问题、提供信息和协助您完成各种任务。请问有什么可以帮助您的？", 'assistant');
                }
            } else {
                console.error('GPUStack API连接失败，状态码:', response.status);
                // 处理连接失败
                handleConnectionFailure();
            }
        } catch (error) {
            // 清除超时
            clearTimeout(timeoutId);

            console.error('GPUStack API连接测试失败:', error);

            // 处理连接失败
            handleConnectionFailure();
        }
    } catch (error) {
        console.error('连接检查完全失败:', error);
        updateServerStatus('error', error.message);

        // 初始化时添加错误消息
        if (chatMessages.children.length === 0) {
            addMessage("警告：无法连接到GPUStack API。您可能无法使用智能助手功能。请检查网络连接或联系管理员。", 'assistant');
        }
    }
}

// 处理连接失败
function handleConnectionFailure() {
    console.error('GPUStack API连接失败');
    updateServerStatus('error', 'GPUStack API连接失败');

    // 初始化时添加错误消息
    if (chatMessages.children.length === 0) {
        addMessage("警告：无法连接到GPUStack API。您可能无法使用智能助手功能。请检查网络连接或联系管理员。", 'assistant');
    }
}

// 自动调整文本区域高度
function autoResizeTextarea() {
    messageInput.style.height = 'auto';
    messageInput.style.height = (messageInput.scrollHeight > 150 ? 150 : messageInput.scrollHeight) + 'px';
}

// 发送消息 - 使用流式响应显示思考过程和最终结果
function sendMessage() {
    const message = messageInput.value.trim();
    if (!message) return;

    // 添加用户消息到聊天界面
    addMessage(message, 'user');

    // 清空输入框并重置高度
    messageInput.value = '';
    messageInput.style.height = '50px';

    // 禁用发送按钮
    sendButton.disabled = true;

    // 显示加载指示器
    loadingIndicator.style.display = 'flex';

    // 显示进度条
    progressContainer.style.display = 'block';
    simulateProgress();

    // 将用户消息添加到历史记录
    messageHistory.push({ role: 'user', content: message });

    // 添加调试信息
    console.log('发送问题:', message);
    console.log('当前消息历史:', messageHistory);

    // 更新状态指示器
    updateServerStatus('checking', '发送中');

    // 记录开始时间
    const startTime = new Date();

    // 创建一个新的消息元素，用于显示助手的回复
    const assistantMessageDiv = document.createElement('div');
    assistantMessageDiv.className = 'message assistant-message';

    // 创建思考过程元素
    const thinkingDiv = document.createElement('div');
    thinkingDiv.className = 'thinking-process';

    // 创建最终答案元素
    const finalAnswerDiv = document.createElement('div');
    finalAnswerDiv.className = 'final-answer';

    // 创建时间戳
    const timeDiv = document.createElement('div');
    timeDiv.className = 'message-time';
    timeDiv.textContent = getCurrentTime();

    // 添加到消息元素
    assistantMessageDiv.appendChild(thinkingDiv);
    assistantMessageDiv.appendChild(finalAnswerDiv);
    assistantMessageDiv.appendChild(timeDiv);

    // 添加到聊天界面
    chatMessages.appendChild(assistantMessageDiv);

    // 滚动到底部
    scrollToBottom();

    // 用于存储思考过程和最终答案
    let thinkingProcess = '';
    let finalAnswer = '';

    // 调用API获取回复，使用流式响应

    // 确保思考过程框可见
    thinkingDiv.style.display = 'block';
    finalAnswerDiv.style.display = 'none';

    // 创建思考内容容器（如果尚未创建）
    if (!thinkingDiv.querySelector('.thinking-content')) {
        thinkingDiv.innerHTML = '<div class="thinking-content"></div>';
    }

    // 获取思考内容容器
    const thinkingContent = thinkingDiv.querySelector('.thinking-content');

    callAssistantAPI(message, (content, isThinking, isDone) => {
        // 处理每个数据块
        console.log('收到数据:', content.length > 50 ? content.substring(0, 50) + '...' : content,
                   isThinking ? '(思考内容)' : '(最终内容)',
                   isDone ? '(完成)' : '(继续中)');

        if (isThinking) {
            // 思考内容 - 显示在思考过程区域
            if (content && content.trim()) {
                console.log('收到思考内容:', content.length > 50 ? content.substring(0, 50) + '...' : content);

                // 更新思考过程
                thinkingProcess = content;

                // 更新显示区域
                thinkingContent.textContent = thinkingProcess;

                // 确保思考过程框可见
                thinkingDiv.style.display = 'block';
                finalAnswerDiv.style.display = 'none';

                console.log('思考过程已更新，长度:', content.length);
            }
        } else {
            // 最终内容 - 直接显示，不包含思考过程
            if (content && content.trim()) {
                console.log('收到最终答案:', content.length > 50 ? content.substring(0, 50) + '...' : content);

                // 当开始显示结果时，隐藏思考过程框
                thinkingDiv.style.display = 'none';
                finalAnswerDiv.style.display = 'block';

                // 更新最终答案
                finalAnswer = content;

                // 使用流式方式更新最终答案
                finalAnswerDiv.innerHTML = formatMessage(finalAnswer);

                console.log('最终答案已更新，长度:', finalAnswer.length);

                // 如果已完成，更新状态
                if (isDone) {
                    console.log('响应完成');
                }
            }
        }

        // 滚动到底部
        scrollToBottom();
    })
    .then(response => {
        // 隐藏加载指示器
        loadingIndicator.style.display = 'none';

        // 停止进度条动画
        stopProgress();

        // 计算响应时间
        const endTime = new Date();
        const responseTime = endTime - startTime;
        console.log('响应时间:', responseTime, 'ms');

        // 更新状态指示器
        updateServerStatus('connected', `响应时间: ${responseTime}ms`);

        // 处理完成后，确保最终答案已正确显示
        console.log('响应完成，确保最终答案已正确显示');

        // 确保思考过程框隐藏，最终答案框显示
        thinkingDiv.style.display = 'none';
        finalAnswerDiv.style.display = 'block';

        // 将助手回复添加到历史记录（只保存最终答案）
        messageHistory.push({ role: 'assistant', content: finalAnswer.trim() || response.trim() });

        // 启用发送按钮
        sendButton.disabled = false;
    })
    .catch(error => {
        // 隐藏加载指示器
        loadingIndicator.style.display = 'none';

        // 停止进度条动画
        stopProgress();

        console.error('调用API错误:', error);

        // 更新状态指示器
        updateServerStatus('error', error.message);

        // 显示错误消息
        thinkingDiv.style.display = 'none';
        finalAnswerDiv.style.display = 'block';
        finalAnswerDiv.innerHTML = `抱歉，发生了错误: ${error.message}`;

        // 尝试连接测试
        setTimeout(() => {
            checkServerConnection();
        }, 1000);

        // 启用发送按钮
        sendButton.disabled = false;
    });
}

// 添加消息到聊天界面
function addMessage(text, sender) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;

    // 处理Markdown格式（简单实现）
    const formattedText = formatMessage(text);

    // 如果是助手消息，直接显示在最终答案区域，不显示思考过程
    if (sender === 'assistant') {
        // 创建最终答案元素
        const finalAnswerDiv = document.createElement('div');
        finalAnswerDiv.className = 'final-answer';
        finalAnswerDiv.innerHTML = formattedText;
        messageDiv.appendChild(finalAnswerDiv);
    } else {
        // 用户消息直接显示
        messageDiv.innerHTML = formattedText;
    }

    // 添加时间戳
    const timeDiv = document.createElement('div');
    timeDiv.className = 'message-time';
    timeDiv.textContent = getCurrentTime();
    messageDiv.appendChild(timeDiv);

    chatMessages.appendChild(messageDiv);

    // 如果消息包含LaTeX公式，触发MathJax渲染
    if (text.includes('$') && typeof MathJax !== 'undefined') {
        try {
            // 延迟一点时间，确保DOM已更新
            setTimeout(() => {
                MathJax.typesetPromise([messageDiv]).catch((err) => {
                    console.error('MathJax渲染错误:', err);
                });
            }, 100);
        } catch (e) {
            console.error('MathJax渲染失败:', e);
        }
    }

    // 滚动到底部
    scrollToBottom();
}

// 格式化消息（增强的Markdown支持）
function formatMessage(text) {
    // 如果是增量更新，可能不需要包装在<p>标签中
    const isIncremental = arguments.length > 1 && arguments[1] === true;

    // 保存LaTeX公式，避免被其他Markdown处理规则干扰
    const latexBlocks = [];
    let latexBlockIndex = 0;

    // 保存块级LaTeX公式 ($$...$$)
    text = text.replace(/\$\$([\s\S]*?)\$\$/g, (match, formula) => {
        const placeholder = `__LATEX_BLOCK_${latexBlockIndex}__`;
        latexBlocks[latexBlockIndex] = {
            type: 'block',
            formula: formula.trim()
        };
        latexBlockIndex++;
        return placeholder;
    });

    // 保存行内LaTeX公式 ($...$)
    text = text.replace(/\$([^\$\n]+?)\$/g, (match, formula) => {
        const placeholder = `__LATEX_INLINE_${latexBlockIndex}__`;
        latexBlocks[latexBlockIndex] = {
            type: 'inline',
            formula: formula.trim()
        };
        latexBlockIndex++;
        return placeholder;
    });

    // 处理代码块，保留原始格式并识别语言
    text = text.replace(/```(\w*)\n([\s\S]*?)```/g, (_, language, codeContent) => {
        // 移除代码块中多余的空行，但保留代码格式
        const formattedCode = codeContent.replace(/\n{3,}/g, '\n\n');

        // 添加复制按钮
        const copyButton = '<button class="code-copy-btn" onclick="copyCodeToClipboard(this)">复制</button>';

        // 如果指定了语言，添加语言类
        const languageClass = language ? ` class="language-${language}"` : '';

        return `<pre${languageClass}>${copyButton}<code>${formattedCode}</code></pre>`;
    });

    // 处理行内代码
    text = text.replace(/`([^`]+)`/g, '<code>$1</code>');

    // 处理链接
    text = text.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');

    // 处理图片
    text = text.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" style="max-width:100%;">');

    // 处理标题 (h1 - h6)
    text = text.replace(/^# (.*?)$/gm, '<h1>$1</h1>');
    text = text.replace(/^## (.*?)$/gm, '<h2>$1</h2>');
    text = text.replace(/^### (.*?)$/gm, '<h3>$1</h3>');
    text = text.replace(/^#### (.*?)$/gm, '<h4>$1</h4>');
    text = text.replace(/^##### (.*?)$/gm, '<h5>$1</h5>');
    text = text.replace(/^###### (.*?)$/gm, '<h6>$1</h6>');

    // 处理粗体文本 (必须在斜体之前处理)
    text = text.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');

    // 处理斜体文本
    text = text.replace(/\*([^*]+)\*/g, '<em>$1</em>');

    // 处理引用块
    text = text.replace(/^>\s+(.*?)$/gm, '<blockquote>$1</blockquote>');

    // 处理水平分割线
    text = text.replace(/^(---|\*\*\*|___)$/gm, '<hr>');

    // 处理任务列表
    text = text.replace(/- \[ \] (.*?)$/gm, '<div class="task-list-item"><input type="checkbox" disabled> $1</div>');
    text = text.replace(/- \[x\] (.*?)$/gm, '<div class="task-list-item"><input type="checkbox" checked disabled> $1</div>');

    // 处理有序列表
    // 先将连续的有序列表项分组，允许列表项之间有一个空行
    let olMatches = text.match(/^(\d+\.\s+.+?)(?=(?:\n{1,2}\d+\.\s+)|$)/gms);
    if (olMatches) {
        for (let match of olMatches) {
            // 清理列表项之间的多余空行
            let cleanedMatch = match.replace(/\n\n+/g, '\n');

            // 提取列表项和它们的序号
            const listItemsWithNumbers = cleanedMatch.split(/\n/).map(item => {
                // 提取序号和内容
                const numberMatch = item.match(/^(\d+)\.\s+(.*)$/);
                if (numberMatch) {
                    const number = parseInt(numberMatch[1]);
                    const content = numberMatch[2];
                    return { number, content };
                }
                return null;
            }).filter(item => item !== null);

            // 确定起始序号
            const startNumber = listItemsWithNumbers.length > 0 ? listItemsWithNumbers[0].number : 1;

            // 创建有序列表，设置起始序号
            let olHtml = `<ol start="${startNumber}">`;

            // 添加列表项
            listItemsWithNumbers.forEach(item => {
                olHtml += `<li value="${item.number}">${item.content}</li>`;
            });

            olHtml += '</ol>';

            // 用有序列表替换原文本
            text = text.replace(match, olHtml);
        }
    }

    // 处理无序列表
    // 先将连续的无序列表项分组，允许列表项之间有一个空行
    let ulMatches = text.match(/^([*\-+]\s+.+?)(?=(?:\n{1,2}[*\-+]\s+)|$)/gms);
    if (ulMatches) {
        for (let match of ulMatches) {
            // 清理列表项之间的多余空行
            let cleanedMatch = match.replace(/\n\n+/g, '\n');

            // 提取列表项
            const listItems = cleanedMatch.split(/\n/).map(item => {
                // 移除星号/减号/加号，保留内容
                return item.replace(/^[*\-+]\s+(.*)$/, '<li>$1</li>');
            }).join('');

            // 用无序列表替换原文本
            text = text.replace(match, `<ul>${listItems}</ul>`);
        }
    }

    // 处理表格
    // 查找表格标记
    const tableRegex = /^\|(.+)\|\s*\n\|[-|:]+\|\s*\n(\|.+\|\s*\n)+/gm;
    let tableMatches = text.match(tableRegex);

    if (tableMatches) {
        for (let tableMatch of tableMatches) {
            // 分割表格行
            const tableRows = tableMatch.trim().split('\n');

            // 提取表头
            const headerRow = tableRows[0];
            const headerCells = headerRow.split('|').filter(cell => cell.trim() !== '');

            // 提取对齐信息
            const alignRow = tableRows[1];
            const alignCells = alignRow.split('|').filter(cell => cell.trim() !== '');
            const alignments = alignCells.map(cell => {
                if (cell.trim().startsWith(':') && cell.trim().endsWith(':')) return 'center';
                if (cell.trim().endsWith(':')) return 'right';
                return 'left';
            });

            // 构建HTML表格
            let tableHtml = '<table border="1" cellpadding="5" cellspacing="0" style="border-collapse:collapse;">';

            // 添加表头
            tableHtml += '<thead><tr>';
            headerCells.forEach((cell, index) => {
                const align = alignments[index] || 'left';
                tableHtml += `<th style="text-align:${align};">${cell.trim()}</th>`;
            });
            tableHtml += '</tr></thead>';

            // 添加表体
            tableHtml += '<tbody>';
            for (let i = 2; i < tableRows.length; i++) {
                const dataRow = tableRows[i];
                const dataCells = dataRow.split('|').filter(cell => cell.trim() !== '');

                tableHtml += '<tr>';
                dataCells.forEach((cell, index) => {
                    const align = alignments[index] || 'left';
                    tableHtml += `<td style="text-align:${align};">${cell.trim()}</td>`;
                });
                tableHtml += '</tr>';
            }
            tableHtml += '</tbody></table>';

            // 替换原始表格文本
            text = text.replace(tableMatch, tableHtml);
        }
    }

    // 处理连续的空行，将多个连续空行替换为最多一个空行
    text = text.replace(/\n{3,}/g, '\n\n');

    // 在标题后面只保留一个空行
    text = text.replace(/^(#{1,6}\s+.*?)\n+/gm, '$1\n');

    // 在列表项之间不保留空行
    text = text.replace(/^(\d+\.\s+.*?)\n\n+(\d+\.\s+)/gm, '$1\n$2');
    text = text.replace(/^([*\-+]\s+.*?)\n\n+([*\-+]\s+)/gm, '$1\n$2');

    // 处理代码块中的空行，保留代码格式
    const codeBlocks = [];
    let codeBlockIndex = 0;
    text = text.replace(/```[\s\S]*?```/g, match => {
        const placeholder = `__CODE_BLOCK_${codeBlockIndex}__`;
        codeBlocks[codeBlockIndex] = match;
        codeBlockIndex++;
        return placeholder;
    });

    // 处理段落之间的空行，使用更合理的间距，减少空行
    text = text.replace(/\n\n+/g, '\n');

    // 恢复代码块
    for (let i = 0; i < codeBlocks.length; i++) {
        text = text.replace(`__CODE_BLOCK_${i}__`, codeBlocks[i]);
    }

    // 处理换行 (在处理列表和表格后进行)
    text = text.replace(/\n/g, '<br>');

    // 恢复LaTeX公式
    for (let i = 0; i < latexBlocks.length; i++) {
        const block = latexBlocks[i];
        if (block.type === 'block') {
            // 块级公式
            text = text.replace(
                `__LATEX_BLOCK_${i}__`,
                `<div class="latex-block">$$${block.formula}$$</div>`
            );
        } else {
            // 行内公式
            text = text.replace(
                `__LATEX_INLINE_${i}__`,
                `<span class="latex-inline">$${block.formula}$</span>`
            );
        }
    }

    // 如果是增量更新，直接返回处理后的文本
    if (isIncremental) {
        return text;
    }

    // 否则，包装在<p>标签中
    return `<p>${text}</p>`;
}

// 获取当前时间
function getCurrentTime() {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
}

// 滚动到底部
function scrollToBottom() {
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 模拟进度条
let progressInterval;
function simulateProgress() {
    let width = 0;
    progressBar.style.width = '0%';

    progressInterval = setInterval(() => {
        if (width >= 90) {
            // 到90%后停止，等待实际完成
            clearInterval(progressInterval);
            return;
        }

        // 进度增加速度随进度增加而减慢
        const increment = Math.max(0.5, 5 * (1 - width / 100));
        width += increment;
        progressBar.style.width = width + '%';
    }, 100);
}

// 停止进度条动画
function stopProgress() {
    clearInterval(progressInterval);
    progressBar.style.width = '100%';

    // 完成后短暂延迟隐藏进度条
    setTimeout(() => {
        progressContainer.style.display = 'none';
        progressBar.style.width = '0%';
    }, 300);
}

// 调用智能助手API - 使用流式响应显示思考过程和最终结果
async function callAssistantAPI(question, onStreaming) {
    try {
        // 显示详细的调试信息
        console.log('开始调用智能助手API，问题:', question);

        // 使用GPUStack API
        return await callGPUStackAssistantAPI(question, onStreaming);
    } catch (error) {
        console.error('API调用完全失败:', error);
        throw new Error(`无法连接到智能助手服务: ${error.message}`);
    }
}

// 调用GPUStack API进行智能助手对话
async function callGPUStackAssistantAPI(question, onStreaming) {
    try {
        // 显示详细的调试信息
        console.log('开始调用GPUStack API，问题:', question);

        // API配置 - 使用本地代理服务器转发到GPUStack API
        const API_URL = "/api/gpustack/assistant";
        console.log('GPUStack API端点:', API_URL);

        // 构建提示，要求模型先详细思考，再给出最终答案
        let systemPrompt = "你是一个智能助手，基于DeepSeek大模型开发。你的任务是提供有用、准确、安全的信息和帮助。请先在<think>标签内进行详细思考，然后在</think>标签后给出最终答案。你可以使用LaTeX语法来表示数学公式，行内公式使用单个$符号包裹，块级公式使用两个$$符号包裹。";

        // 构建用户提示
        let userPrompt = `<think>
请先思考如何回答以下问题，分析问题的各个方面，考虑可能的答案和解释。
</think>

问题：${question}`;

        // 准备消息数组
        let messages = [
            {
                role: "system",
                content: systemPrompt
            },
            {
                role: "user",
                content: userPrompt
            }
        ];

        // 如果有历史消息，添加到消息数组中
        if (messageHistory.length > 0) {
            try {
                // 只保留最近的几条消息
                const recentMessages = messageHistory.slice(-4);

                // 将历史消息添加到消息数组中
                messages = [
                    {
                        role: "system",
                        content: systemPrompt
                    }
                ];

                // 添加历史消息
                for (const msg of recentMessages) {
                    messages.push({
                        role: msg.role,
                        content: msg.content
                    });
                }

                // 添加当前问题
                messages.push({
                    role: "user",
                    content: userPrompt
                });

                console.log('包含历史消息的请求:', messages);
            } catch (e) {
                console.error('格式化历史消息失败:', e);
                // 如果失败，使用原始消息数组
            }
        }

        // 构建请求体
        const requestBody = {
            messages: messages,
            model: "deepseek-r1",
            stream: true, // 启用流式输出
            temperature: 0.3,
            top_p: 0.8,
            max_tokens: 4096,
            frequency_penalty: 0.1,
            presence_penalty: 0.1
        };

        // 详细的调试信息
        console.log('发送GPUStack API请求体:', JSON.stringify(requestBody, null, 2));

        // 发送请求并记录时间
        const startTime = new Date();
        console.log('开始发送请求时间:', startTime.toISOString());

        // 设置超时
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 120000); // 120秒超时

        // 添加重试机制
        let response;
        let retryCount = 0;
        const maxRetries = 2;

        while (retryCount <= maxRetries) {
            try {
                console.log(`尝试调用GPUStack API (尝试 ${retryCount + 1}/${maxRetries + 1})...`);

                // 发送请求
                response = await fetch(API_URL, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify(requestBody),
                    signal: controller.signal
                });

                // 清除超时
                clearTimeout(timeoutId);

                if (!response.ok) {
                    console.error('GPUStack API响应错误状态码:', response.status);
                    const errorText = await response.text();
                    console.error('GPUStack API错误响应内容:', errorText);

                    let errorData;
                    try {
                        errorData = JSON.parse(errorText);
                    } catch (e) {
                        console.error('解析错误响应失败:', e);
                    }

                    throw new Error(errorData?.error || `GPUStack API请求失败: ${response.status} - ${response.statusText}`);
                }

                // 如果成功，跳出循环
                break;
            } catch (error) {
                retryCount++;
                console.error(`GPUStack API调用失败 (尝试 ${retryCount}/${maxRetries + 1}):`, error.message);

                // 如果已经达到最大重试次数，抛出错误
                if (retryCount > maxRetries) {
                    throw error;
                }

                // 等待一段时间后重试
                const retryDelay = 3000 * retryCount; // 递增延迟
                console.log(`将在 ${retryDelay}ms 后重试...`);
                await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
        }

        // 处理流式响应
        console.log('开始处理GPUStack API流式响应');

        // 创建读取器和解码器
        const reader = response.body.getReader();
        const decoder = new TextDecoder('utf-8', { fatal: false });

        // 用于存储完整响应的变量
        let fullContent = '';
        let buffer = ''; // 用于存储不完整的数据行

        // 读取流式响应
        while (true) {
            const { done, value } = await reader.read();

            if (done) {
                console.log('流式响应读取完成');
                break;
            }

            // 解码二进制数据
            let chunk;
            try {
                chunk = decoder.decode(value, { stream: true });
            } catch (e) {
                console.error('解码数据块时出错:', e);
                chunk = new TextDecoder('utf-8', { fatal: false }).decode(value);
            }

            // 处理数据块
            buffer += chunk;

            // 按行分割数据
            const lines = buffer.split('\n');
            buffer = lines.pop() || ''; // 最后一行可能不完整，保存到buffer中

            // 处理每一行数据
            for (const line of lines) {
                // 跳过空行
                if (!line.trim()) continue;

                // 检查是否是SSE格式的数据行
                if (line.startsWith('data: ')) {
                    const data = line.substring(6);

                    // 检查是否是结束标记
                    if (data === '[DONE]') {
                        console.log('收到流式响应结束标记');
                        continue;
                    }

                    try {
                        // 解析JSON数据
                        const json = JSON.parse(data);

                        // 检查是否包含delta内容
                        if (json.choices && json.choices.length > 0 && json.choices[0].delta) {
                            const delta = json.choices[0].delta;

                            // 提取内容
                            if (delta.content) {
                                const content = delta.content;
                                fullContent += content;

                                // 查找最后一个</think>标签的位置
                                const thinkEndIndex = fullContent.lastIndexOf('</think>');

                                if (thinkEndIndex !== -1) {
                                    // 找到了</think>标签，只输出它之后的内容
                                    const displayContent = fullContent.substring(thinkEndIndex + 8);

                                    // 只有当有内容可显示时才更新
                                    if (displayContent && onStreaming) {
                                        onStreaming(displayContent, false, false);
                                    }
                                } else {
                                    // 没有找到</think>标签，说明思考过程尚未结束
                                    // 可以选择显示思考过程或等待完整结果
                                    if (onStreaming) {
                                        onStreaming(fullContent, true, false);
                                    }
                                }
                            }
                        }
                    } catch (e) {
                        console.error('解析数据行失败:', e, line);
                    }
                }
            }
        }

        // 处理最终结果
        console.log('流式响应处理完成，提取最终结果');

        // 查找最后一个</think>标签的位置
        const thinkEndIndex = fullContent.lastIndexOf('</think>');
        let finalResult;

        if (thinkEndIndex !== -1) {
            // 找到了</think>标签，只使用它之后的内容作为最终结果
            finalResult = fullContent.substring(thinkEndIndex + 8);
            console.log('最终结果提取完成，长度:', finalResult.length);
        } else {
            // 如果没有找到</think>标签，则使用完整内容
            console.log('未找到</think>标签，使用完整内容作为结果');
            finalResult = fullContent;
        }

        // 通知流式处理已完成
        if (onStreaming) {
            onStreaming(finalResult, false, true);
        }

        const endTime = new Date();
        console.log('请求完成时间:', endTime.toISOString());
        console.log('请求耗时(毫秒):', endTime - startTime);

        return finalResult;
    } catch (error) {
        console.error('GPUStack API调用失败:', error);
        throw new Error(`无法连接到GPUStack API服务: ${error.message}`);
    }
}

// 复制代码到剪贴板
function copyCodeToClipboard(button) {
    const pre = button.parentElement;
    const code = pre.querySelector('code');
    const text = code.textContent;

    // 创建临时文本区域
    const textarea = document.createElement('textarea');
    textarea.value = text;
    document.body.appendChild(textarea);
    textarea.select();

    try {
        // 执行复制命令
        const successful = document.execCommand('copy');

        // 更新按钮文本提示复制成功
        if (successful) {
            const originalText = button.textContent;
            button.textContent = '已复制!';
            button.style.backgroundColor = 'rgba(16, 185, 129, 0.2)';

            // 2秒后恢复原始文本
            setTimeout(() => {
                button.textContent = originalText;
                button.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
            }, 2000);
        }
    } catch (err) {
        console.error('复制失败:', err);
    }

    // 移除临时文本区域
    document.body.removeChild(textarea);
}

// 初始化
document.addEventListener('DOMContentLoaded', init);
