// 定义全局变量来存储原始选择的位置信息
let translationOriginalRange = null;
let translationOriginalText = "";

// 翻译功能初始化 - 在taskpane加载时调用
function initTranslationFeature() {
    // 页面加载时获取选中的文本
    const selectedText = getSelectedTextForTranslation();

    // 如果有选中的文本，填充到输入框
    if (selectedText) {
        setTranslationInputText(selectedText);
        updateTranslationCharCount(); // 更新字符计数
    } else {
        // 初始化字符计数
        updateTranslationCharCount();
    }



    // 确保结果区域和状态消息有正确的类名
    const translatedText = document.getElementById('translatedText');
    if (translatedText) {
        translatedText.className = 'result-box';
        translatedText.style.display = 'block';
    }

    const statusMessage = document.getElementById('statusMessage');
    if (statusMessage) {
        statusMessage.className = 'status-message';
    }

    // 禁用复制和替换按钮
    const copyBtn = document.getElementById('copyBtn');
    const replaceBtn = document.getElementById('replaceBtn');
    if (copyBtn) copyBtn.disabled = true;
    if (replaceBtn) replaceBtn.disabled = true;

    // 获取所有翻译语言按钮并确保它们可用
    const allLanguageButtons = [
        document.getElementById('zhToEnBtn'),
        document.getElementById('enToZhBtn'),
        document.getElementById('customLangBtn')
    ];

    allLanguageButtons.forEach(btn => {
        if (btn) btn.disabled = false;
    });


}

// 翻译文本的主函数
async function translateText(sourceLang = 'zh', targetLang = 'en') {
    // 先获取当前选中的文本
    const selectedText = getSelectedTextForTranslation();
    if (selectedText) {
        // 如果有选中的文本，则更新输入框
        setTranslationInputText(selectedText);
        updateTranslationCharCount();
    } else {
        // 如果没有新选中的文本，但输入框中有内容，且与上次选中的文本不同
        // 则清除原始选择位置，因为用户可能手动修改了输入框
        const inputText = document.getElementById('inputText').value.trim();
        if (inputText && inputText !== translationOriginalText) {
            console.log('输入框内容已更改，清除原始选择位置');
            translationOriginalRange = null;
            translationOriginalText = "";
        }
    }

    // 源语言现在由用户指定，不再使用自动检测
    console.log('用户指定的源语言：', sourceLang);

    // 获取输入框中的文本
    const inputText = document.getElementById('inputText').value.trim();
    const outputDiv = document.getElementById('translatedText');
    const loadingDiv = document.getElementById('loading');
    const copyBtn = document.getElementById('copyBtn');



    // 获取所有翻译语言按钮
    const allLanguageButtons = [
        document.getElementById('zhToEnBtn'),
        document.getElementById('enToZhBtn'),
        document.getElementById('customLangBtn')
    ];

    // 验证输入
    if (!inputText) {
        showTranslationError(outputDiv, '请输入要翻译的文本');
        return;
    }

    // 准备请求
    // 清空输出区域，准备接收新内容
    outputDiv.innerHTML = '';
    outputDiv.style.display = 'none';
    loadingDiv.style.display = 'block';
    outputDiv.className = 'result-box';



    // 禁用所有翻译语言按钮
    allLanguageButtons.forEach(btn => {
        if (btn) btn.disabled = true;
    });

    // 禁用复制和替换按钮
    copyBtn.disabled = true;
    document.getElementById('replaceBtn').disabled = true;

    try {
        console.log('开始翻译，源语言:', sourceLang, '目标语言:', targetLang);
        console.log('输入文本:', inputText);

        // 用于存储完整响应
        let displayText = "";

        // 定义变量用于存储完整响应
        let completeResponse = "";

        // 定义回调函数，用于处理流式响应
        const onStreaming = (content) => {
            // 流式接收响应
            console.log('收到数据块:', content.length > 30 ? content.substring(0, 30) + '...' : content);

            // 累加到完整响应
            displayText += content;
            completeResponse += content;

            // 检查是否已经收到完整的思考过程（有结束标签）
            if (completeResponse.includes('</think>')) {
                // 使用正则表达式一次性移除所有思考过程
                const finalText = completeResponse.replace(/<think>([\s\S]*?)<\/think>/gi, '').replace(/^[\r\n]+/, '');

                // 如果有最终结果，显示它
                if (finalText.trim()) {
                    // 格式化并显示结果
                    outputDiv.style.display = 'block';
                    outputDiv.className = 'result-box success';
                    outputDiv.innerHTML = formatTextWithIndent(finalText);

                    // 启用复制和替换按钮
                    document.getElementById('copyBtn').disabled = false;
                    document.getElementById('replaceBtn').disabled = false;
                }
            } else if (!completeResponse.includes('<think>')) {
                // 如果没有思考过程标签，直接显示内容
                outputDiv.style.display = 'block';
                outputDiv.className = 'result-box success';
                outputDiv.innerHTML = formatTextWithIndent(completeResponse);

                // 启用复制和替换按钮
                document.getElementById('copyBtn').disabled = false;
                document.getElementById('replaceBtn').disabled = false;
            }
            // 如果有<think>标签但没有</think>标签，不显示任何内容，等待完整响应
        };

        // 调用翻译API，传入回调函数以处理流式响应
        try {
            // 传递原始输入文本，用于后续清理翻译结果
            await callTranslationAPI(inputText, onStreaming, sourceLang, targetLang, inputText);
            console.log('翻译API调用成功');
        } catch (apiError) {
            console.error('翻译API调用失败:', apiError);
            throw apiError; // 重新抛出错误，让外层catch块处理
        }

        // 处理完成后
        console.log('翻译完成，最终响应长度:', displayText.length);

        // 清理可能的思考标签并显示最终结果
        const finalText = displayText.replace(/<think>([\s\S]*?)<\/think>/gi, '').replace(/^[\r\n]+/, '');
        console.log('清理思考标签后的结果长度:', finalText.length);

        // 格式化最终结果
        const formattedResult = formatTextWithIndent(finalText);

        // 显示最终结果
        outputDiv.style.display = 'block';
        outputDiv.className = 'result-box success';
        outputDiv.innerHTML = formattedResult;

        // 处理完成后
        loadingDiv.style.display = 'none';



        // 启用复制和替换按钮
        document.getElementById('copyBtn').disabled = false;
        document.getElementById('replaceBtn').disabled = false;

        // 翻译按钮可以立即启用
        allLanguageButtons.forEach(btn => {
            if (btn) btn.disabled = false;
        });

    } catch (error) {
        // 隐藏加载指示器
        loadingDiv.style.display = 'none';
        // 显示错误信息
        outputDiv.style.display = 'block';
        showTranslationError(outputDiv, `翻译失败: ${error.message}`);
        console.error('API请求错误:', error);

        // 出错时重新启用所有按钮
        allLanguageButtons.forEach(btn => {
            if (btn) btn.disabled = false;
        });
        document.getElementById('copyBtn').disabled = true;
        document.getElementById('replaceBtn').disabled = true;

    }
}



// 显示翻译错误信息
function showTranslationError(element, message) {
    // 直接显示错误信息，不使用打字机效果
    element.style.display = 'block';
    element.innerHTML = message;
    element.className = 'result-box error';

    // 获取所有翻译语言按钮
    const allLanguageButtons = [
        document.getElementById('zhToEnBtn'),
        document.getElementById('enToZhBtn'),
        document.getElementById('customLangBtn')
    ];

    // 确保翻译按钮可用，其他按钮禁用
    allLanguageButtons.forEach(btn => {
        if (btn) btn.disabled = false;
    });
    document.getElementById('copyBtn').disabled = true;
    document.getElementById('replaceBtn').disabled = true;

    // 隐藏状态消息
    const statusMessage = document.getElementById('statusMessage');
    if (statusMessage) statusMessage.style.display = 'none';
}

// 显示自定义语言对话框
function showCustomLanguageDialog() {
    document.getElementById('customLangDialog').style.display = 'flex';
}

// 关闭自定义语言对话框
function closeCustomLanguageDialog() {
    document.getElementById('customLangDialog').style.display = 'none';
}

// 使用自定义语言进行翻译
function translateWithCustomLanguage() {
    const sourceLang = document.getElementById('sourceLang').value;
    const targetLang = document.getElementById('targetLang').value;

    // 关闭对话框
    closeCustomLanguageDialog();

    // 调用翻译函数
    translateText(sourceLang, targetLang);
}

// 调用千问API进行翻译 - 使用增量更新实时显示内容
async function callTranslationAPI(text, onStreaming, sourceLang = 'zh', targetLang = 'en', originalText = '') {
    // 获取状态和输出元素
    const debugDiv = document.getElementById('statusMessage');
    const outputDiv = document.getElementById('translatedText');

    // 隐藏状态消息框
    debugDiv.style.display = 'none';

    // 清空输出区域，准备接收新内容
    outputDiv.innerText = '';
    outputDiv.style.display = 'block';
    outputDiv.className = 'result-box success';



    // 启用复制和替换按钮，因为我们会实时更新内容
    document.getElementById('copyBtn').disabled = false;
    document.getElementById('replaceBtn').disabled = false;

    // API配置 - 使用本地代理服务器转发到千问服务器
    const API_URL = "/api/proxy/generate";

    // 源语言现在由用户指定，不再使用自动检测
    console.log('使用用户指定的源语言：', sourceLang);

    // 如果目标语言与源语言相同，则选择英语作为默认目标语言
    if (targetLang === sourceLang) {
        targetLang = (sourceLang === 'en') ? 'zh' : 'en';
        console.log('目标语言与源语言相同，自动切换为：', targetLang);
    }

    // 构建翻译提示，针对不同语言对进行优化
    let prompt = "";

    // 所有翻译方向使用统一的提示词格式，只是语言方向不同
    if (sourceLang === 'en' && targetLang === 'zh') {
        // 英译中
        prompt = `请将以下英文文本翻译成中文：

${text}

要求：
- 翻译要准确完整
- 每个段落开头缩进两个空格
- 段落之间不要有空行
- 保持原文的段落结构
- 直接输出翻译结果，不要包含原文或其他说明`;
    }
    else if (sourceLang === 'zh' && targetLang === 'en') {
        // 中译英
        prompt = `请将以下中文文本翻译成英文：

${text}

要求：
- 翻译要准确完整
- 每个段落开头缩进两个空格
- 段落之间不要有空行
- 保持原文的段落结构
- 直接输出翻译结果，不要包含原文或其他说明`;
    }
    else {
        // 其他语言对
        prompt = `请将以下${getLanguageName(sourceLang)}文本翻译成${getLanguageName(targetLang)}：

${text}

要求：
- 翻译要准确完整
- 每个段落开头缩进两个空格
- 段落之间不要有空行
- 保持原文的段落结构
- 直接输出翻译结果，不要包含原文或其他说明`;
    }

    try {
        // 准备请求体 - 使用千问API的格式
        const requestBody = {
            model: "qwen3:30b-a3b",
            prompt: prompt,
            stream: true,
            options: {
                temperature: 0.3,  // 翻译需要更低的温度以保证准确性
                num_predict: 3000
            }
        };

        console.log('发送API请求:', API_URL);
        console.log('请求体:', JSON.stringify(requestBody, null, 2));

        // 记录开始时间
        const startTime = new Date();
        console.log('请求开始时间:', startTime.toISOString());

        // 发送API请求
        const response = await fetch(API_URL, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(requestBody)
        });

        console.log('响应状态:', response.status, response.statusText);

        if (!response.ok) {
            console.error('响应不成功:', response.status, response.statusText);
            try {
                const errorData = await response.json().catch(() => null);
                console.error('错误数据:', errorData);
                throw new Error(errorData?.error || `API请求失败: ${response.status}`);
            } catch (jsonError) {
                console.error('解析错误响应失败:', jsonError);
                throw new Error(`API请求失败: ${response.status} - ${response.statusText}`);
            }
        } else {
            console.log('响应成功，开始读取数据');
        }

        // 使用 ReadableStream 逐步读取数据
        console.log('初始化 ReadableStream 读取器');
        const reader = response.body.getReader();
        const decoder = new TextDecoder("utf-8");
        let fullResponse = ""; // 用于累积完整内容
        let readCount = 0;

        while (true) {
            console.log(`读取数据块 #${++readCount}`);
            const { done, value } = await reader.read();

            if (done) {
                console.log('数据读取完成');
                break;
            }

            console.log(`收到数据块，大小: ${value ? value.length : 0} 字节`);
            const chunk = decoder.decode(value, { stream: true });

            try {
                // 尝试直接解析整个响应
                const lines = chunk.split('\n').filter(l => l.trim());
                console.log(`解析到 ${lines.length} 行数据`);

                for (const line of lines) {
                    try {
                        const data = JSON.parse(line);

                        // 根据 Ollama 的响应格式提取内容
                        // Ollama 可能使用 response 或 content 字段
                        const content = data.response || data.content || data.message?.content || "";

                        if (content) {
                            // 记录收到的内容
                            console.log(`提取到内容: ${content.length > 20 ? content.substring(0, 20) + '...' : content}`);

                            // 累积内容到完整响应
                            fullResponse += content;

                            // 始终将内容传递给onStreaming，让它决定是否显示
                            // onStreaming函数会检查是否包含思考过程标签，并只显示最终结果
                            onStreaming?.(content);
                        } else {
                            console.log('数据中没有内容字段:', data);
                        }
                    } catch (e) {
                        console.error("解析行数据错误:", e, "原始数据:", line);
                    }
                }
            } catch (e) {
                console.error("处理响应错误:", e, "原始数据:", chunk);
            }
        }

        // 数据读取完成后，进行最终处理
        // 如果没有提取到任何内容，显示错误消息
        if (!fullResponse) {
            console.error("未能提取到任何内容");
            outputDiv.innerText = "无法获取翻译结果，请重试";
            return "无法获取翻译结果，请重试";
        }

        // 清理可能的思考标签并显示最终结果
        let finalText = "";

        // 使用正则表达式移除所有思考过程
        // 这个正则表达式会匹配<think>和</think>之间的所有内容，包括嵌套的标签
        const thinkRegex = /<think>([\s\S]*?)<\/think>/gi;

        // 检查是否有思考过程标签
        if (fullResponse.includes('<think>') && fullResponse.includes('</think>')) {
            // 移除所有思考过程
            finalText = fullResponse.replace(thinkRegex, '').trim();
            // 移除开头的空行
            finalText = finalText.replace(/^[\r\n]+/, '');
            console.log('使用正则表达式移除所有思考过程');
        } else if (fullResponse.includes('<think>')) {
            // 如果只有开始标签但没有结束标签
            console.log('警告：找到思考过程开始标签但没有结束标签');

            // 尝试使用<think>标签分割
            const parts = fullResponse.split('<think>');
            if (parts.length > 0 && parts[0].trim()) {
                // 如果<think>前有内容，使用它
                finalText = parts[0].trim();
                console.log('使用<think>标签前的内容作为结果');
            } else {
                // 否则尝试查找明显的分隔符
                const possibleResults = fullResponse.split(/\n\s*\n/);
                // 使用最后一个非空段落
                for (let i = possibleResults.length - 1; i >= 0; i--) {
                    if (possibleResults[i].trim() && !possibleResults[i].includes('<think>')) {
                        finalText = possibleResults[i].trim();
                        console.log('使用最后一个非空段落作为结果');
                        break;
                    }
                }
            }
        } else {
            // 如果没有思考过程标签，使用完整响应
            finalText = fullResponse.trim();
            console.log('没有思考过程标签，使用完整响应');
        }

        console.log('清理思考标签后的结果长度:', finalText.length);

        // 清理翻译结果，确保不包含原文
        const cleanedResult = cleanTranslationResult(finalText, originalText);

        // 格式化最终结果
        const formattedResult = formatTextWithIndent(cleanedResult);

        // 记录结束时间和总耗时
        const endTime = new Date();
        console.log('请求完成时间:', endTime.toISOString());
        console.log('请求总耗时(毫秒):', endTime - startTime);

        return formattedResult;

    } catch (error) {
        console.error('API调用失败:', error);
        // 在输出区域显示错误信息，而不是在状态框中
        outputDiv.innerText = `翻译失败: ${error.message}`;
        outputDiv.className = 'result-box error';
        throw new Error(`无法连接到翻译服务: ${error.message}`);
    }
}

// 检测文本语言
function detectLanguage(text) {
    // 简单的语言检测逻辑
    const chineseRegex = /[\u4e00-\u9fa5]/;
    const japaneseRegex = /[\u3040-\u30ff\u3400-\u4dbf\u4e00-\u9fff\uf900-\ufaff\uff66-\uff9f]/;
    const koreanRegex = /[\uac00-\ud7af\u1100-\u11ff\u3130-\u318f\ua960-\ua97f\ud7b0-\ud7ff]/;

    if (chineseRegex.test(text) && !japaneseRegex.test(text) && !koreanRegex.test(text)) {
        return 'zh';
    } else if (japaneseRegex.test(text) && !koreanRegex.test(text)) {
        return 'ja';
    } else if (koreanRegex.test(text)) {
        return 'ko';
    } else {
        // 默认假设是英文或其他拉丁字母语言
        return 'en';
    }
}

// 获取语言名称
function getLanguageName(langCode) {
    const langMap = {
        'en': '英语',
        'zh': '中文',
        'ja': '日语',
        'ko': '韩语',
        'fr': '法语',
        'de': '德语',
        'es': '西班牙语',
        'ru': '俄语',
        'auto': '自动检测的语言'
    };
    return langMap[langCode] || langCode;
}

// 打字机效果函数（针对翻译）
function typeWriterForTranslation(element, text, speed = 20) {
    // 预处理文本，确保段落格式正确
    // 1. 将多个连续的换行符替换为单个换行符（去除空行）
    text = text.replace(/\n\s*\n/g, '\n');

    // 2. 确保每个段落开头缩进两个空格
    text = text.replace(/\n/g, '\n  ');

    // 3. 确保第一个段落也有缩进
    if (!text.startsWith('  ')) {
        text = '  ' + text;
    }

    let i = 0;
    element.innerHTML = '';

    // 先禁用复制和替换按钮，等打字机效果完成后再启用
    document.getElementById('copyBtn').disabled = true;
    document.getElementById('replaceBtn').disabled = true;

    function typing() {
        if (i < text.length) {
            // 处理换行符
            if (text.charAt(i) === '\n') {
                element.innerHTML += '<br>';
            } else {
                element.innerHTML += text.charAt(i);
            }
            i++;
            setTimeout(typing, speed);
        } else {
            // 打字机效果完成后启用复制和替换按钮
            document.getElementById('copyBtn').disabled = false;
            document.getElementById('replaceBtn').disabled = false;
        }
    }

    typing();
}

// 复制翻译后的文本到剪贴板 - 直接调用ribbon.js中的copyPolishedText函数
function copyTranslatedText() {
    const translatedText = document.getElementById('translatedText').innerText;
    const statusMsg = document.getElementById('statusMessage');

    if (!translatedText) {
        statusMsg.innerText = '没有可复制的内容';
        statusMsg.className = 'status-message status-error';
        return;
    }

    try {
        // 先将translatedText的内容复制到polishedText
        // 这样可以直接使用ribbon.js中的copyPolishedText函数
        const polishedTextElement = document.getElementById('polishedText');

        // 保存原始内容（如果有）
        const originalPolishedText = polishedTextElement ? polishedTextElement.innerText : '';

        // 如果polishedText元素不存在，则创建一个临时的
        let tempElement = null;
        if (!polishedTextElement) {
            tempElement = document.createElement('div');
            tempElement.id = 'polishedText';
            tempElement.style.display = 'none';
            document.body.appendChild(tempElement);
        }

        // 设置内容
        const elementToUse = polishedTextElement || tempElement;
        elementToUse.innerText = translatedText;

        // 调用ribbon.js中的ribbonCopyText函数
        if (typeof window.ribbonCopyText === 'function') {
            window.ribbonCopyText();
        } else {
            // 如果函数不可用，则使用备用方法
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(translatedText)
                    .then(() => {
                        statusMsg.innerText = '已复制到剪贴板';
                        statusMsg.className = 'status-message status-success';
                        setTimeout(() => {
                            statusMsg.innerText = '';
                            statusMsg.className = 'status-message';
                        }, 2000);
                    })
                    .catch(err => {
                        throw err;
                    });
            } else {
                throw new Error('无法复制文本');
            }
        }

        // 恢复原始内容或移除临时元素
        if (polishedTextElement) {
            polishedTextElement.innerText = originalPolishedText;
        } else if (tempElement) {
            document.body.removeChild(tempElement);
        }
    } catch (err) {
        console.error('复制失败:', err);
        statusMsg.innerText = `复制失败: ${err.message || '未知错误'}`;
        statusMsg.className = 'status-message status-error';

        // 显示手动复制提示
        showManualCopyTipForTranslation(statusMsg);
    }
}

// 显示手动复制提示
function showManualCopyTipForTranslation(statusElement) {
    statusElement.innerHTML = '自动复制失败，请手动选中文本并使用 Ctrl+C 复制';
    statusElement.className = 'status-message status-error';

    // 高亮显示结果区域，提示用户选中
    const resultBox = document.getElementById('translatedText');
    resultBox.style.boxShadow = '0 0 0 2px rgba(244, 67, 54, 0.5)';

    // 3秒后恢复正常样式
    setTimeout(() => {
        resultBox.style.boxShadow = '';
        statusElement.className = 'status-message';
    }, 5000);
}

// 更新字符计数
function updateTranslationCharCount() {
    const inputElement = document.getElementById('inputText');
    const text = inputElement ? inputElement.value : '';
    const count = text ? text.length : 0;

    const charCountElement = document.getElementById('charCount');
    if (charCountElement) {
        charCountElement.innerText = `字符数: ${count}`;
    }
}

// 清理翻译结果，确保不包含原文
function cleanTranslationResult(translationResult, originalText) {
    if (!translationResult || !originalText) return translationResult;

    // 如果翻译结果包含完整的原文，可能是模型将原文也输出了
    if (translationResult.includes(originalText.trim())) {
        console.log('检测到翻译结果包含完整原文，尝试移除');

        // 尝试移除原文
        let cleanedResult = translationResult.replace(originalText.trim(), '').trim();

        // 如果移除后结果为空，返回原始结果
        if (!cleanedResult) return translationResult;

        return cleanedResult;
    }

    // 检查是否有明显的分隔，如空行分隔的原文和翻译
    const paragraphs = translationResult.split(/\n\s*\n/);

    // 如果只有一个段落，直接返回
    if (paragraphs.length <= 1) return translationResult;

    // 检查第一段是否与原文相似（可能是原文的重复）
    const firstPara = paragraphs[0].trim();
    const restParas = paragraphs.slice(1).join('\n\n').trim();

    // 如果第一段与原文相似度高，可能是原文重复
    if (isSimilarText(firstPara, originalText)) {
        console.log('检测到第一段与原文相似，只保留后续段落');
        return restParas;
    }

    // 检查语言特征
    const hasChineseInFirstPara = /[\u4e00-\u9fa5]/.test(firstPara);
    const hasChineseInRestPara = /[\u4e00-\u9fa5]/.test(restParas);

    // 如果语言特征不同，可能是原文+翻译的情况
    if (hasChineseInFirstPara !== hasChineseInRestPara) {
        console.log('检测到语言特征不同，可能是原文+翻译混合');

        // 检查原文的语言特征
        const hasChineseInOriginal = /[\u4e00-\u9fa5]/.test(originalText);

        // 根据原文和结果的语言特征决定保留哪部分
        if (hasChineseInOriginal && !hasChineseInRestPara) {
            // 中译英：原文是中文，保留非中文部分（后面的段落）
            console.log('中译英情况，保留非中文部分');
            return restParas;
        } else if (!hasChineseInOriginal && hasChineseInRestPara) {
            // 英译中：原文是英文，保留中文部分（后面的段落）
            console.log('英译中情况，保留中文部分');
            return restParas;
        }
    }

    // 默认返回原始结果
    return translationResult;
}

// 检查两段文本的相似度
function isSimilarText(text1, text2) {
    // 简单实现：检查较短文本的前20个字符是否出现在较长文本中
    const shortText = text1.length < text2.length ? text1 : text2;
    const longText = text1.length < text2.length ? text2 : text1;

    // 取前20个字符（或全部，如果不足20个）
    const prefix = shortText.substring(0, 20).trim();

    // 检查前缀是否出现在较长文本中
    return longText.includes(prefix);
}

// 格式化文本，添加缩进
function formatTextWithIndent(text) {
    // 如果文本为空，直接返回
    if (!text || text.trim() === '') return '';

    // 移除思考过程
    // 使用正则表达式移除所有思考过程
    const thinkRegex = /<think>([\s\S]*?)<\/think>/gi;

    // 检查是否有思考过程标签
    if (text.includes('<think>') && text.includes('</think>')) {
        // 移除所有思考过程
        text = text.replace(thinkRegex, '').trim();
        // 移除开头的空行
        text = text.replace(/^[\r\n]+/, '');
    } else if (text.includes('<think>')) {
        // 如果只有开始标签但没有结束标签
        // 尝试使用<think>标签分割
        const parts = text.split('<think>');
        if (parts.length > 0 && parts[0].trim()) {
            // 如果<think>前有内容，使用它
            text = parts[0].trim();
        }
    }

    // 移除可能的"翻译结果："等前缀
    text = text.replace(/^(翻译结果[:：]|Translation result[:：]|最终翻译[:：]|Final translation[:：])\s*/i, '');

    // 重新分割处理后的文本
    const processedParagraphs = text.split(/\n+/);

    // 处理每个段落，添加缩进
    const formattedParagraphs = processedParagraphs.map(para => {
        // 去除可能已存在的缩进
        const trimmedPara = para.trim();
        // 如果不是空行，添加缩进
        if (trimmedPara.length > 0) {
            // 检查是否已经有缩进
            if (!trimmedPara.startsWith('　　') && !trimmedPara.startsWith('  ')) {
                return `　　${trimmedPara}`;
            }
            return trimmedPara;
        }
        return '';
    }).filter(para => para !== ''); // 过滤掉空行

    // 将处理后的段落重新组合成文本
    return formattedParagraphs.join('\n');
}



// 替换原文本的函数 - 直接实现替换功能，不依赖ribbon.js
function replaceWithTranslation() {
    const translatedText = document.getElementById('translatedText').innerText;
    const statusMsg = document.getElementById('statusMessage');
    const replaceBtn = document.getElementById('replaceBtn');

    if (!translatedText) {
        statusMsg.innerText = '没有可替换的内容';
        statusMsg.className = 'status-message status-error';
        return;
    }

    // 检查是否有保存的原始选择位置
    if (!translationOriginalRange) {
        statusMsg.innerText = '无法找到原始选择位置，请重新选择文本';
        statusMsg.className = 'status-message status-error';
        return;
    }

    // 禁用按钮并显示状态
    replaceBtn.disabled = true;
    statusMsg.innerText = '正在替换原文...';
    statusMsg.className = 'status-message';

    // 使用setTimeout来确保状态更新先显示出来
    setTimeout(() => {
        try {
            // 检查是否有Application对象
            if (window.Application && window.Application.ActiveDocument) {
                // 使用保存的原始选择位置
                if (translationOriginalRange) {
                    // 保存当前的选择区域（如果有），以便在替换后恢复
                    let currentSelection = null;
                    try {
                        currentSelection = window.Application.Selection.Range;
                        console.log('已保存当前选择区域位置');
                    } catch (e) {
                        console.log('无法保存当前选择区域:', e);
                    }

                    // 选中原始区域
                    try {
                        translationOriginalRange.Select();
                        console.log('已选中原始区域');
                    } catch (e) {
                        console.error('选中原始区域失败:', e);
                        throw new Error('无法选中原始区域: ' + e.message);
                    }

                    // 使用全新的方法替换文本，确保只替换选中的文本
                    try {
                        // 获取WPS应用程序对象
                        const app = window.Application;
                        const doc = app.ActiveDocument;
                        const selection = app.Selection;

                        // 保存原始选择区域的起始和结束位置
                        const startPos = translationOriginalRange.Start;
                        const endPos = translationOriginalRange.End;
                        console.log(`原始选择区域位置: 从 ${startPos} 到 ${endPos}`);

                        // 选中原始区域
                        translationOriginalRange.Select();
                        console.log('已选中原始区域');

                        // 使用精确的方法替换文本
                        // 方法1: 使用TypeText方法
                        try {
                            // 确保翻译文本的段落格式正确
                            // 将文本按段落分割，确保每段都有正确的缩进
                            const paragraphs = translatedText.split('\n');
                            const formattedParagraphs = paragraphs.map(para => {
                                // 去除可能已存在的缩进
                                const trimmedPara = para.replace(/^[\s　]+/, '');
                                // 如果不是空行，添加缩进
                                if (trimmedPara.length > 0) {
                                    return `　　${trimmedPara}`;
                                }
                                return para;
                            });

                            // 先删除选中的文本
                            selection.Delete();

                            // 将格式化后的文本组合成一个字符串，使用段落标记分隔
                            const formattedText = formattedParagraphs
                                .filter(para => para.trim().length > 0)
                                .join('\r\n');

                            // 在文本末尾添加一个换行符
                            const textWithNewline = formattedText + '\r\n';

                            // 一次性插入所有文本，保持段落格式
                            selection.TypeText(textWithNewline);

                            console.log('使用TypeText方法替换成功');
                        } catch (typeTextError) {
                            console.error('TypeText方法失败:', typeTextError);

                            // 方法2: 尝试使用剪贴板方法
                            try {
                                // 确保翻译文本的段落格式正确
                                const paragraphs = translatedText.split('\n');
                                const formattedParagraphs = paragraphs.map(para => {
                                    // 去除可能已存在的缩进
                                    const trimmedPara = para.replace(/^[\s　]+/, '');
                                    // 如果不是空行，添加缩进
                                    if (trimmedPara.length > 0) {
                                        return `　　${trimmedPara}`;
                                    }
                                    return para;
                                });

                                // 重新组合文本，使用段落标记，过滤空行
                                const formattedText = formattedParagraphs
                                    .filter(para => para.trim().length > 0)
                                    .join('\r\n');

                                // 在文本末尾添加一个换行符
                                const textWithNewline = formattedText + '\r\n';

                                // 先将格式化后的文本复制到剪贴板
                                const oldClipboard = app.System.ClipboardText;
                                app.System.ClipboardText = textWithNewline;

                                // 选中原始区域
                                doc.Range(startPos, endPos).Select();

                                // 删除选中内容并粘贴
                                selection.Delete();
                                selection.Paste();

                                // 恢复剪贴板原有内容
                                app.System.ClipboardText = oldClipboard;
                                console.log('使用剪贴板方法替换成功');
                            } catch (clipboardError) {
                                console.error('剪贴板方法失败:', clipboardError);

                                // 方法3: 尝试使用最原始的方法
                                try {
                                    // 重新选中原始区域
                                    doc.Range(startPos, endPos).Select();

                                    // 确保翻译文本的段落格式正确
                                    const paragraphs = translatedText.split('\n');
                                    const formattedParagraphs = paragraphs.map(para => {
                                        // 去除可能已存在的缩进
                                        const trimmedPara = para.replace(/^[\s　]+/, '');
                                        // 如果不是空行，添加缩进
                                        if (trimmedPara.length > 0) {
                                            return `　　${trimmedPara}`;
                                        }
                                        return para;
                                    });

                                    // 重新组合文本，使用段落标记，过滤空行
                                    const formattedText = formattedParagraphs
                                        .filter(para => para.trim().length > 0)
                                        .join('\r\n');

                                    // 在文本末尾添加一个换行符
                                    const textWithNewline = formattedText + '\r\n';

                                    // 直接设置Text属性
                                    selection.Text = textWithNewline;
                                    console.log('使用Text属性方法替换成功');
                                } catch (textError) {
                                    console.error('Text属性方法失败:', textError);
                                    throw new Error('所有替换方法均失败');
                                }
                            }
                        }

                        console.log('成功替换原文为翻译后的文本');
                    } catch (e) {
                        console.error('替换文本失败:', e);
                        throw new Error('无法替换文本: ' + e.message);
                    }

                    // 如果用户在翻译过程中移动了光标，尝试恢复到原来的位置
                    if (currentSelection) {
                        try {
                            currentSelection.Select();
                            console.log('已恢复到用户当前的光标位置');
                        } catch (e) {
                            console.log('恢复光标位置失败，但这不影响替换操作:', e);
                        }
                    }

                    statusMsg.innerText = '原文已替换成功';
                    statusMsg.className = 'status-message status-success';
                    setTimeout(() => {
                        statusMsg.innerText = '';
                        statusMsg.className = 'status-message';
                    }, 2000);

                    // 清除翻译功能的选择范围，因为已经替换完成
                    translationOriginalRange = null;
                    translationOriginalText = "";

                    // 替换成功后保持按钮禁用状态，防止重复替换
                    replaceBtn.disabled = true;
                } else {
                    throw new Error('无法获取原始选中区域');
                }
            } else {
                throw new Error('无法访问WPS文档');
            }
        } catch (error) {
            console.error('替换原文失败:', error);
            statusMsg.innerText = `替换原文失败: ${error.message}`;
            statusMsg.className = 'status-message status-error';

            // 如果出错，清除原始选择位置，让用户重新选择
            translationOriginalRange = null;
            translationOriginalText = "";

            // 出错时重新启用按钮，允许用户重试
            replaceBtn.disabled = false;
        }
    }, 100); // 小延时确保 UI 更新
}

// 清空所有内容并重置表单
function clearTranslation() {
    // 清空输入框
    document.getElementById('inputText').value = '';

    // 确保思考过程容器隐藏
    const thinkingDiv = document.getElementById('thinkingDiv');
    if (thinkingDiv) {
        thinkingDiv.innerHTML = '';
        thinkingDiv.style.display = 'none';
    }

    // 清空结果区域
    const translatedText = document.getElementById('translatedText');
    if (translatedText) {
        translatedText.innerHTML = '';
        translatedText.className = 'result-box';
        translatedText.style.display = 'block';
    }

    // 重置状态消息
    const statusMessage = document.getElementById('statusMessage');
    if (statusMessage) {
        statusMessage.innerText = '';
        statusMessage.className = 'status-message';
    }

    // 禁用复制和替换按钮
    const copyBtn = document.getElementById('copyBtn');
    const replaceBtn = document.getElementById('replaceBtn');
    if (copyBtn) copyBtn.disabled = true;
    if (replaceBtn) replaceBtn.disabled = true;

    // 清除原始选择位置
    translationOriginalRange = null;
    translationOriginalText = "";

    // 更新字符计数
    updateTranslationCharCount();
}

// 获取WPS文档中选中的文本并保存选择位置
function getSelectedTextForTranslation() {
    try {
        // 检查是否有Application对象
        if (window.Application && window.Application.Selection) {
            const selection = window.Application.Selection;

            // 获取选择类型（如果可用）
            let selectionType = null;
            try {
                selectionType = selection.Type;
                console.log('选择类型:', selectionType);
            } catch (e) {
                console.log('无法获取选择类型:', e);
            }

            const selectedText = selection.Text;
            console.log('选中文本:', selectedText, '长度:', selectedText ? selectedText.length : 0);

            // 检查是否是有效的选择
            // 1. 文本必须存在且非空
            // 2. 文本长度必须大于1（避免单个字符的选择）
            if (selectedText && selectedText.trim() && selectedText.trim().length > 1) {
                const trimmedText = selectedText.trim();

                // 保存原始选择的位置信息
                try {
                    // 保存选择区域的范围对象
                    translationOriginalRange = selection.Range;
                    // 保存选中的文本
                    translationOriginalText = trimmedText;

                    console.log('已保存有效的原始选择位置');
                } catch (rangeError) {
                    console.error('保存选择位置失败:', rangeError);
                }

                return trimmedText;
            } else {
                console.log('没有有效的选中文本或仅有单个字符');
            }
        }
    } catch (error) {
        console.error('获取选中文本失败:', error);
    }
    return null;
}

// 设置输入框的文本
function setTranslationInputText(text) {
    const inputElement = document.getElementById('inputText');
    if (text) {
        // 处理文本，确保第一段有正确的缩进
        // 检查文本是否已经有缩进
        if (!text.startsWith('　　') && !text.startsWith('  ')) {
            // 如果没有缩进，添加全角空格缩进
            const paragraphs = text.split('\n');
            // 为第一段添加缩进
            if (paragraphs.length > 0 && paragraphs[0].trim().length > 0) {
                paragraphs[0] = '　　' + paragraphs[0].trim();
            }
            // 处理其他段落
            for (let i = 1; i < paragraphs.length; i++) {
                if (paragraphs[i].trim().length > 0 && !paragraphs[i].startsWith('　　') && !paragraphs[i].startsWith('  ')) {
                    paragraphs[i] = '　　' + paragraphs[i].trim();
                }
            }
            text = paragraphs.join('\n');
        }

        // 清除默认文本并设置新文本
        inputElement.value = text;
        // 清除之前的翻译结果
        document.getElementById('translatedText').innerHTML = '';
        document.getElementById('translatedText').className = 'result-box';
        // 重置状态消息
        document.getElementById('statusMessage').innerText = '';
        document.getElementById('statusMessage').className = 'status-message';
    }
}

// 导出翻译函数，使其可以被其他脚本访问
window.ribbonTranslate = callTranslationAPI;

console.log('translation.js: 已导出翻译函数 ribbonTranslate');
