// 模板管理功能相关的JavaScript代码

// 定义全局变量来存储模板数据
let templateLibrary = {
    official: [], // 官方模板
    custom: []    // 自定义模板
};

// 定义全局变量来存储原始选择的位置信息（用于模板套用）
let templateOriginalRange = null;

// 定义全局变量来存储模板修改映射关系（原模板ID -> 修改后的模板ID）
let templateModifiedMap = {};

// 模板管理功能初始化 - 在taskpane加载时调用
function initTemplateFeature() {
    console.log('初始化模板管理功能');

    // 加载模板库
    loadTemplateLibrary();

    // 初始化UI元素
    initTemplateUI();

    // 绑定事件处理函数
    bindTemplateEvents();
}

// 加载模板库
function loadTemplateLibrary() {
    console.log('加载模板库');

    // 尝试从本地存储加载自定义模板
    const savedCustomTemplates = localStorage.getItem('customTemplates');
    if (savedCustomTemplates) {
        try {
            templateLibrary.custom = JSON.parse(savedCustomTemplates);
            console.log('已加载自定义模板:', templateLibrary.custom.length, '个');
        } catch (error) {
            console.error('加载自定义模板失败:', error);
            templateLibrary.custom = [];
        }
    }

    // 尝试从本地存储加载模板修改映射关系
    const savedTemplateModifiedMap = localStorage.getItem('templateModifiedMap');
    if (savedTemplateModifiedMap) {
        try {
            templateModifiedMap = JSON.parse(savedTemplateModifiedMap);
            console.log('已加载模板修改映射关系');
        } catch (error) {
            console.error('加载模板修改映射关系失败:', error);
            templateModifiedMap = {};
        }
    }

    // 加载官方模板库（预设的模板）
    templateLibrary.official = [
        {
            id: 'official-1',
            name: '党政机关公文',
            category: '公文',
            description: '适用于党政机关公文写作，包含标准格式和规范用语',
            content: `
【发文机关】
【发文字号】

【标题】

【正文】

【落款】
【日期】
            `.trim()
        },
        {
            id: 'official-2',
            name: '会议纪要',
            category: '会议',
            description: '标准会议纪要模板，包含会议基本信息、议程、决议等',
            content: `
【会议名称】会议纪要

时间：【会议时间】
地点：【会议地点】
主持人：【主持人】
参会人员：【参会人员】
记录人：【记录人】

一、会议议程
【会议议程】

二、会议内容
【会议内容】

三、会议决议
【会议决议】

四、下一步工作安排
【工作安排】

【落款】
【日期】
            `.trim()
        },
        {
            id: 'official-3',
            name: '工作报告',
            category: '报告',
            description: '工作总结与计划报告模板，适用于各类工作汇报',
            content: `
【标题】

一、工作回顾
【工作回顾内容】

二、主要成绩
【主要成绩内容】

三、存在问题
【存在问题内容】

四、下一步工作计划
【工作计划内容】

【落款】
【日期】
            `.trim()
        },
        {
            id: 'official-4',
            name: '请示报告',
            category: '报告',
            description: '向上级机关请示工作事项的标准格式',
            content: `
【发文机关】
【发文字号】

关于【请示事项】的请示

【正文】

此请示。

【落款】
【日期】
            `.trim()
        },
        {
            id: 'official-5',
            name: '通知公告',
            category: '公告',
            description: '适用于各类通知、公告的标准格式',
            content: `
【发文机关】
【发文字号】

关于【通知事项】的通知

【正文】

特此通知。

【落款】
【日期】
            `.trim()
        },
        {
            id: 'official-6',
            name: '学术论文模板',
            category: '学术论文',
            description: '标准学术论文格式，包含摘要、关键词、引言等结构',
            content: `
【论文标题】

摘要：【摘要内容】

关键词：【关键词1】；【关键词2】；【关键词3】

一、引言
【引言内容】

二、研究背景与意义
【研究背景与意义内容】

三、研究方法
【研究方法内容】

四、研究结果与分析
【研究结果与分析内容】

五、结论
【结论内容】

参考文献：
【参考文献列表】
            `.trim()
        },
        {
            id: 'official-7',
            name: '商业计划书',
            category: '商业文档',
            description: '完整的商业计划书模板，适用于创业融资或项目提案',
            content: `
【公司/项目名称】商业计划书

执行摘要：
【执行摘要内容】

一、公司/项目概述
【公司/项目概述内容】

二、市场分析
【市场分析内容】

三、产品/服务描述
【产品/服务描述内容】

四、营销策略
【营销策略内容】

五、运营管理
【运营管理内容】

六、财务预测
【财务预测内容】

七、融资需求
【融资需求内容】

八、风险分析
【风险分析内容】

九、附件
【附件内容】
            `.trim()
        },

        {
            id: 'official-8',
            name: '合同协议模板',
            category: '法律文书',
            description: '标准合同协议模板，包含各方权利义务、违约责任等条款',
            content: `
【合同名称】

甲方：【甲方名称】
法定代表人：【法定代表人姓名】
地址：【甲方地址】
联系电话：【甲方电话】

乙方：【乙方名称】
法定代表人：【法定代表人姓名】
地址：【乙方地址】
联系电话：【乙方电话】

甲乙双方本着平等自愿、公平公正的原则，经友好协商，就【合同事项】达成如下协议：

第一条 合同标的
【合同标的内容】

第二条 合同期限
【合同期限内容】

第三条 合同金额及支付方式
【合同金额及支付方式内容】

第四条 双方权利与义务
【双方权利与义务内容】

第五条 违约责任
【违约责任内容】

第六条 争议解决
【争议解决内容】

第七条 其他条款
【其他条款内容】

本合同一式两份，甲乙双方各持一份，具有同等法律效力。

甲方（盖章）：【甲方盖章位置】
乙方（盖章）：【乙方盖章位置】

签约日期：【签约日期】
            `.trim()
        }
    ];

    console.log('已加载官方模板:', templateLibrary.official.length, '个');

    // 显示模板列表
    displayTemplateList();
}

// 初始化UI元素
function initTemplateUI() {
    console.log('初始化模板管理UI');

    // 初始化模板分类筛选
    initTemplateCategoryFilter();
}

// 初始化模板分类筛选
function initTemplateCategoryFilter() {
    // 获取所有模板分类
    const categories = new Set();

    // 从官方模板和自定义模板中收集所有分类
    templateLibrary.official.forEach(template => {
        if (template.category) categories.add(template.category);
    });

    templateLibrary.custom.forEach(template => {
        if (template.category) categories.add(template.category);
    });

    // 获取分类筛选下拉菜单
    const categoryFilter = document.getElementById('categoryFilter');
    if (categoryFilter) {
        // 清空现有选项（保留"全部"选项）
        while (categoryFilter.options.length > 1) {
            categoryFilter.remove(1);
        }

        // 添加分类选项
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category;
            categoryFilter.appendChild(option);
        });
    }
}

// 绑定事件处理函数
function bindTemplateEvents() {
    console.log('绑定模板管理事件');

    // 分类筛选事件
    const categoryFilter = document.getElementById('categoryFilter');
    if (categoryFilter) {
        categoryFilter.addEventListener('change', function() {
            displayTemplateList(this.value);
        });
    }

    // 搜索框事件
    const searchInput = document.getElementById('searchTemplate');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.trim().toLowerCase();
            const category = categoryFilter ? categoryFilter.value : 'all';
            displayTemplateList(category, searchTerm);
        });
    }

    // 创建新模板按钮事件
    const createTemplateBtn = document.getElementById('createTemplateBtn');
    if (createTemplateBtn) {
        createTemplateBtn.addEventListener('click', function() {
            showCreateTemplateDialog();
        });
    }


}

// 显示模板列表
function displayTemplateList(category = 'all', searchTerm = '') {
    console.log('显示模板列表, 分类:', category, '搜索词:', searchTerm);

    const templateListContainer = document.getElementById('templateList');
    if (!templateListContainer) return;

    // 清空现有列表
    templateListContainer.innerHTML = '';

    // 合并官方和自定义模板
    const allTemplates = [...templateLibrary.official, ...templateLibrary.custom];

    // 筛选模板
    const filteredTemplates = allTemplates.filter(template => {
        // 分类筛选
        const categoryMatch = category === 'all' || template.category === category;

        // 搜索词筛选
        const searchMatch = !searchTerm ||
            template.name.toLowerCase().includes(searchTerm) ||
            (template.description && template.description.toLowerCase().includes(searchTerm));

        return categoryMatch && searchMatch;
    });

    // 如果没有匹配的模板
    if (filteredTemplates.length === 0) {
        templateListContainer.innerHTML = '<div class="no-templates">没有找到匹配的模板</div>';
        return;
    }

    // 显示筛选后的模板
    filteredTemplates.forEach(template => {
        const templateCard = createTemplateCard(template);
        templateListContainer.appendChild(templateCard);
    });
}

// 创建模板卡片元素
function createTemplateCard(template) {
    const card = document.createElement('div');
    card.className = 'template-card';
    card.setAttribute('data-template-id', template.id);

    // 判断是否为官方模板
    const isOfficial = template.id.startsWith('official-');

    // 创建模板卡片内容
    card.innerHTML = `
        <div class="template-header">
            <h3 class="template-name">${template.name}</h3>
            <span class="template-badge ${isOfficial ? 'official' : 'custom'}">${isOfficial ? '官方' : '自定义'}</span>
        </div>
        <div class="template-category">${template.category || '未分类'}</div>
        <div class="template-description">${template.description || '无描述'}</div>
        <div class="template-actions">
            <button class="btn btn-secondary btn-apply" onclick="applyTemplate('${template.id}')">套用</button>
            <button class="btn btn-secondary btn-preview" onclick="previewTemplate('${template.id}')">预览</button>
            <button class="btn btn-secondary btn-edit" onclick="editTemplateFormat('${template.id}')">修改格式</button>
            ${!isOfficial ? `<button class="btn btn-secondary btn-delete" onclick="deleteTemplate('${template.id}')">删除</button>` : ''}
        </div>
    `;

    return card;
}

// 显示创建模板对话框
function showCreateTemplateDialog(initialContent = '') {
    console.log('显示创建模板对话框');

    const createDialog = document.getElementById('createTemplateDialog');
    if (!createDialog) return;

    // 重置表单
    const templateNameInput = document.getElementById('templateName');
    const templateCategoryInput = document.getElementById('templateCategory');
    const templateDescriptionInput = document.getElementById('templateDescription');
    const templateContentInput = document.getElementById('templateContent');

    if (templateNameInput) templateNameInput.value = '';
    // 不设置默认分类，让用户自行选择
    if (templateCategoryInput) templateCategoryInput.selectedIndex = 0; // 选择第一个选项
    if (templateDescriptionInput) templateDescriptionInput.value = '';
    if (templateContentInput) templateContentInput.value = initialContent;

    // 显示对话框
    createDialog.style.display = 'flex';
}

// 关闭创建模板对话框
function closeCreateTemplateDialog() {
    const createDialog = document.getElementById('createTemplateDialog');
    if (createDialog) {
        createDialog.style.display = 'none';
    }
}

// 保存新模板
function saveNewTemplate() {
    console.log('保存新模板');

    // 获取表单数据
    const templateName = document.getElementById('templateName').value.trim();
    const templateCategory = document.getElementById('templateCategory').value.trim();
    const templateDescription = document.getElementById('templateDescription').value.trim();
    const templateContent = document.getElementById('templateContent').value.trim();

    // 验证必填字段
    if (!templateName) {
        alert('请输入模板名称');
        return;
    }

    if (!templateContent) {
        alert('请输入模板内容');
        return;
    }

    // 创建新模板对象
    const newTemplate = {
        id: 'custom-' + Date.now(), // 使用时间戳作为唯一ID
        name: templateName,
        category: templateCategory || '未分类',
        description: templateDescription,
        content: templateContent,
        createdAt: new Date().toISOString()
    };

    // 添加到自定义模板库
    templateLibrary.custom.push(newTemplate);

    // 保存到本地存储
    saveCustomTemplates();

    // 关闭对话框
    closeCreateTemplateDialog();

    // 刷新模板列表和分类筛选
    initTemplateCategoryFilter();
    displayTemplateList();

    // 显示成功消息
    showStatusMessage('模板创建成功', 'success');
}

// 保存自定义模板到本地存储
function saveCustomTemplates() {
    try {
        localStorage.setItem('customTemplates', JSON.stringify(templateLibrary.custom));
        console.log('自定义模板已保存到本地存储');
    } catch (error) {
        console.error('保存自定义模板失败:', error);
        alert('保存模板失败: ' + error.message);
    }
}

// 保存模板修改映射关系到本地存储
function saveTemplateModifiedMap() {
    try {
        localStorage.setItem('templateModifiedMap', JSON.stringify(templateModifiedMap));
        console.log('模板修改映射关系已保存到本地存储');
    } catch (error) {
        console.error('保存模板修改映射关系失败:', error);
    }
}

// 预览模板
function previewTemplate(templateId) {
    console.log('预览模板:', templateId);

    // 查找模板
    const template = findTemplateById(templateId);
    if (!template) {
        alert('未找到模板');
        return;
    }

    // 获取预览对话框
    const previewDialog = document.getElementById('previewTemplateDialog');
    const previewTitle = document.getElementById('previewTemplateTitle');
    const previewContent = document.getElementById('previewTemplateContent');

    if (!previewDialog || !previewTitle || !previewContent) return;

    // 设置预览内容
    previewTitle.textContent = template.name;
    previewContent.textContent = template.content;

    // 显示预览对话框
    previewDialog.style.display = 'flex';
}

// 关闭预览对话框
function closePreviewDialog() {
    const previewDialog = document.getElementById('previewTemplateDialog');
    if (previewDialog) {
        previewDialog.style.display = 'none';
    }
}

// 套用模板
function applyTemplate(templateId) {
    console.log('套用模板:', templateId);

    // 检查是否有修改过的版本
    const modifiedTemplateId = templateModifiedMap[templateId];
    if (modifiedTemplateId) {
        console.log('找到修改过的模板版本:', modifiedTemplateId);
        templateId = modifiedTemplateId;
    }

    // 查找模板
    const template = findTemplateById(templateId);
    if (!template) {
        alert('未找到模板');
        return;
    }

    // 获取当前文档
    const doc = window.Application.ActiveDocument;
    if (!doc) {
        alert('当前没有打开任何文档');
        return;
    }

    try {
        // 获取当前选择范围
        const selection = window.Application.Selection;
        if (!selection) {
            alert('无法获取当前选择范围');
            return;
        }

        // 插入模板内容
        selection.TypeText(template.content);

        // 显示成功消息
        showStatusMessage('模板套用成功', 'success');
    } catch (error) {
        console.error('套用模板失败:', error);
        alert('套用模板失败: ' + error.message);
    }
}

// 删除模板
function deleteTemplate(templateId) {
    console.log('删除模板:', templateId);

    // 确认删除
    if (!confirm('确定要删除这个模板吗？此操作不可撤销。')) {
        return;
    }

    // 查找模板索引
    const templateIndex = templateLibrary.custom.findIndex(t => t.id === templateId);
    if (templateIndex === -1) {
        alert('未找到模板或无法删除官方模板');
        return;
    }

    // 获取要删除的模板
    const templateToDelete = templateLibrary.custom[templateIndex];

    // 从数组中删除
    templateLibrary.custom.splice(templateIndex, 1);

    // 如果这个模板是某个官方模板的修改版本，也需要从映射关系中删除
    if (templateToDelete.originalTemplateId) {
        for (const [originalId, modifiedId] of Object.entries(templateModifiedMap)) {
            if (modifiedId === templateId) {
                delete templateModifiedMap[originalId];
                break;
            }
        }
        // 保存修改后的映射关系
        saveTemplateModifiedMap();
    }

    // 保存到本地存储
    saveCustomTemplates();

    // 刷新模板列表和分类筛选
    initTemplateCategoryFilter();
    displayTemplateList();

    // 显示成功消息
    showStatusMessage('模板已删除', 'success');
}

// 根据ID查找模板
function findTemplateById(templateId) {
    // 先在官方模板中查找
    let template = templateLibrary.official.find(t => t.id === templateId);

    // 如果没找到，在自定义模板中查找
    if (!template) {
        template = templateLibrary.custom.find(t => t.id === templateId);
    }

    return template;
}

// 获取当前文档中选中的文本
function getSelectedTextForTemplate() {
    try {
        const doc = window.Application.ActiveDocument;
        if (!doc) return '';

        const selection = window.Application.Selection;
        if (!selection) return '';

        // 保存原始选择范围，用于后续替换
        templateOriginalRange = selection.Range;

        // 获取选中的文本
        return selection.Text;
    } catch (error) {
        console.error('获取选中文本失败:', error);
        return '';
    }
}

// 显示状态消息
function showStatusMessage(message, type = 'info') {
    const statusMessage = document.getElementById('statusMessage');
    if (!statusMessage) return;

    statusMessage.textContent = message;
    statusMessage.className = 'status-message';

    if (type === 'success') {
        statusMessage.classList.add('status-success');
    } else if (type === 'error') {
        statusMessage.classList.add('status-error');
    }

    // 3秒后自动清除消息
    setTimeout(() => {
        statusMessage.textContent = '';
        statusMessage.className = 'status-message';
    }, 3000);
}

// 编辑模板格式
function editTemplateFormat(templateId) {
    console.log('编辑模板格式:', templateId);

    // 查找模板
    const template = findTemplateById(templateId);
    if (!template) {
        alert('未找到模板');
        return;
    }

    // 获取编辑格式对话框
    const editFormatDialog = document.getElementById('editFormatDialog');
    const editFormatTitle = document.getElementById('editFormatTitle');
    const editFormatContent = document.getElementById('editFormatContent');
    const templateIdInput = document.getElementById('editTemplateId');

    if (!editFormatDialog || !editFormatTitle || !editFormatContent || !templateIdInput) {
        alert('编辑对话框元素不存在');
        return;
    }

    // 设置对话框内容
    editFormatTitle.textContent = `编辑模板格式: ${template.name}`;
    editFormatContent.value = template.content;
    templateIdInput.value = templateId;

    // 显示对话框
    editFormatDialog.style.display = 'flex';
}

// 关闭编辑格式对话框
function closeEditFormatDialog() {
    const editFormatDialog = document.getElementById('editFormatDialog');
    if (editFormatDialog) {
        editFormatDialog.style.display = 'none';
    }
}

// 保存模板格式修改
function saveTemplateFormat() {
    console.log('保存模板格式修改');

    // 获取表单数据
    const templateId = document.getElementById('editTemplateId').value;
    const newContent = document.getElementById('editFormatContent').value.trim();

    // 验证必填字段
    if (!newContent) {
        alert('模板内容不能为空');
        return;
    }

    // 查找模板
    const template = findTemplateById(templateId);
    if (!template) {
        alert('未找到模板');
        return;
    }

    // 判断是官方模板还是自定义模板
    const isOfficial = templateId.startsWith('official-');

    if (isOfficial) {
        // 如果是官方模板，直接修改官方模板的内容
        const templateIndex = templateLibrary.official.findIndex(t => t.id === templateId);
        if (templateIndex === -1) {
            alert('未找到模板');
            return;
        }

        // 直接更新官方模板内容
        templateLibrary.official[templateIndex].content = newContent;
        templateLibrary.official[templateIndex].updatedAt = new Date().toISOString();

        // 显示成功消息
        showStatusMessage('模板格式已更新', 'success');
    } else {
        // 如果是自定义模板，直接更新
        const templateIndex = templateLibrary.custom.findIndex(t => t.id === templateId);
        if (templateIndex === -1) {
            alert('未找到模板');
            return;
        }

        // 更新模板内容
        templateLibrary.custom[templateIndex].content = newContent;
        templateLibrary.custom[templateIndex].updatedAt = new Date().toISOString();

        // 保存到本地存储
        saveCustomTemplates();

        // 显示成功消息
        showStatusMessage('模板格式已更新', 'success');
    }

    // 关闭对话框
    closeEditFormatDialog();

    // 刷新模板列表
    displayTemplateList();
}

// 应用格式化工具到模板内容
function applyFormatTool(tool) {
    console.log('应用格式化工具:', tool);

    const editFormatContent = document.getElementById('editFormatContent');
    if (!editFormatContent) return;

    // 获取当前内容和选中范围
    const content = editFormatContent.value;
    const selectionStart = editFormatContent.selectionStart;
    const selectionEnd = editFormatContent.selectionEnd;
    const selectedText = content.substring(selectionStart, selectionEnd);

    // 如果没有选中文本，则返回
    if (selectionStart === selectionEnd && tool !== 'indent-all' && tool !== 'remove-blank-lines') {
        alert('请先选择要格式化的文本');
        return;
    }

    let newText = '';
    let newContent = '';

    // 根据不同的工具应用不同的格式化
    switch (tool) {
        case 'bold':
            // 加粗（使用【】包围）
            newText = `【${selectedText}】`;
            break;
        case 'indent':
            // 添加缩进（每行前添加两个空格）
            newText = selectedText.split('\n').map(line => `　　${line}`).join('\n');
            break;
        case 'indent-all':
            // 所有段落缩进
            newContent = content.split('\n\n').map(paragraph => {
                if (paragraph.trim().length > 0) {
                    // 如果段落已经有缩进，不再添加
                    if (paragraph.startsWith('　　')) {
                        return paragraph;
                    }
                    return `　　${paragraph.trim()}`;
                }
                return paragraph;
            }).join('\n\n');
            editFormatContent.value = newContent;
            return;
        case 'remove-blank-lines':
            // 移除空行
            newContent = content.split('\n').filter(line => line.trim().length > 0).join('\n');
            editFormatContent.value = newContent;
            return;
        case 'add-placeholder':
            // 添加占位符
            newText = `【${selectedText}】`;
            break;
        case 'add-title':
            // 添加标题格式
            newText = `\n${selectedText}\n`;
            break;
        default:
            return;
    }

    // 替换选中的文本
    editFormatContent.value = content.substring(0, selectionStart) + newText + content.substring(selectionEnd);

    // 重新设置光标位置
    editFormatContent.focus();
    editFormatContent.selectionStart = selectionStart;
    editFormatContent.selectionEnd = selectionStart + newText.length;
}


