<customUI xmlns="http://schemas.microsoft.com/office/2006/01/customui" onLoad="OnAddinLoad">
    <!-- 自定义功能区 -->
    <ribbon startFromScratch="false">
        <tabs>
            <tab id="wpsAddinTab" label="西部钻探AI">
                <!-- 文本处理工具组 -->
				<group id="textToolsGroup" label="文本工具">
                    <button id="btnShowTaskPane" label="文本润色" onAction="OnAction" enabled="true" getImage="GetImage" visible="true" size="large" />
                    <!-- 2*2布局的按钮 -->
                    <box id="boxMain" boxStyle="vertical">
                        <!-- 第一行 -->
                        <box id="boxRow1" boxStyle="horizontal">
                            <button id="btnPolish" label="润色" onAction="OnAction" getImage="GetImage" size="large"/>
                            <button id="btnPartyStyle" label="党政风" onAction="OnAction" getImage="GetImage" size="large"/>
                        </box>
                        <!-- 第二行 -->
                        <box id="boxRow2" boxStyle="horizontal">
                            <button id="btnCondense" label="缩写" onAction="OnAction" getImage="GetImage" size="large"/>
                            <button id="btnExpansion" label="扩写" onAction="OnAction" getImage="GetImage" size="large"/>
                        </box>
                    </box>

                    <separator id="sep1" />
                    <button id="btnTranslation" label="语言翻译" onAction="OnAction" enabled="true" getImage="GetImage" visible="true" size="large" />
                    <!-- 翻译功能的小按钮 - 2*2布局 -->
                    <box id="boxTranslation" boxStyle="vertical">
                        <!-- 第一行 -->
                        <box id="boxTransRow1" boxStyle="horizontal">
                            <button id="btnZhToEn" label="中译英" onAction="OnAction" getImage="GetImage" size="large" screentip="将中文翻译成英文"/>
                            <button id="btnCustomLang" label="自定义翻译" onAction="OnAction" getImage="GetImage" size="large" screentip="自定义源语言和目标语言"/>
                        </box>
                        <!-- 第二行 -->
                        <box id="boxTransRow2" boxStyle="horizontal">
                            <button id="btnEnToZh" label="英译中" onAction="OnAction" getImage="GetImage" size="large" screentip="将英文翻译成中文"/>
                            <!-- 留空位保持对称，可以在这里添加第四个按钮 -->
                        </box>
                    </box>
                </group>

                <!-- 模板管理组 -->
                <group id="templateGroup" label="文档模板">
                    <!-- 将模板管理和专利交底书左对齐排列 -->
                    <!-- 模板管理按钮 -->
                    <button id="btnTemplate" label="模板管理" onAction="OnAction" enabled="true" getImage="GetImage" visible="true" size="large" />
                    <separator id="sep3" />
                    <!-- 专利交底书按钮 -->
                    <button id="btnPatent" label="专利交底书" onAction="OnAction" enabled="true" getImage="GetImage" visible="true" size="large" screentip="生成专利申请技术交底书" />
                </group>

                <!-- 智能助手组 -->
                <group id="assistantGroup" label="智能助手">
                    <button id="btnSmartAssistant" label="智能助手" onAction="OnAction" enabled="true" getImage="GetImage" visible="true" size="large" screentip="打开智能助手问答面板" />
                    <separator id="sep2" />
                    <button id="btnXdecai" label="XDEC·AI" onAction="OnAction" getImage="GetImage" size="large" />
                </group>

                <!-- 公司信息组（装饰性） -->
                <group id="companyInfoGroup" label="公司信息">
                    <button id="btnCompanyLogo" label="西部钻探工程有限公司" onAction="OnAction" getImage="GetImage" enabled="true" size="large" />
                </group>
            </tab>
        </tabs>
    </ribbon>
</customUI>
