<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能助手</title>
    <link rel="stylesheet" href="../css/assistant-modern.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>智能助手</h1>
            <div class="header-controls">
                <div id="serverStatus">
                    <span id="statusIndicator"></span>
                    <span id="statusText">检测中...</span>
                </div>
                <button id="testConnectionBtn">测试连接</button>
            </div>
        </div>
        <div class="chat-container">
            <div id="chatMessages" class="chat-messages">
                <!-- 消息将在这里动态添加 -->
            </div>
            <div id="loadingIndicator" class="loading" style="display: none;">
                <div class="loading-dots">
                    <div class="loading-dot"></div>
                    <div class="loading-dot"></div>
                    <div class="loading-dot"></div>
                </div>
            </div>
            <div id="progressContainer" class="progress-container">
                <div id="progressBar" class="progress-bar"></div>
            </div>
            <div class="input-container">
                <textarea id="messageInput" class="message-input" placeholder="请输入您的问题..." rows="1"></textarea>
                <button id="sendButton" class="send-button"></button>
            </div>
        </div>
    </div>

    <!-- 引入 MathJax 库，用于渲染LaTeX公式 -->
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            svg: {
                fontCache: 'global'
            },
            startup: {
                typeset: false
            }
        };
    </script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>

    <!-- 引入 JavaScript 文件 -->
    <script src="../js/assistant.js"></script>
</body>
</html>
