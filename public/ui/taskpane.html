<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        :root {
            --primary-color: #4a6baf;
            --primary-light: #6b8fd4;
            --primary-dark: #2c4c8c;
            --accent-color: #ff7043;
            --text-color: #333;
            --text-light: #666;
            --bg-color: #f8f9fa;
            --border-color: #e0e0e0;
            --success-color: #4caf50;
            --error-color: #f44336;
            --shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-color);
            background-color: var(--bg-color);
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .card {
            background-color: white;
            border-radius: 12px;
            box-shadow: var(--shadow);
            padding: 30px;
            margin-bottom: 30px;
            transition: var(--transition);
        }

        .card:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        }

        .header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 15px;
        }

        .header h2 {
            color: var(--primary-color);
            font-size: 1.5rem;
            font-weight: 600;
            margin-left: 10px;
        }

        .header-icon {
            color: var(--primary-color);
            font-size: 1.8rem;
        }

        textarea {
            width: 100%;
            min-height: 150px;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-family: inherit;
            font-size: 15px;
            line-height: 1.5;
            resize: vertical;
            transition: var(--transition);
            margin-bottom: 15px;
        }

        textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(74, 107, 175, 0.2);
        }

        .char-count {
            text-align: right;
            font-size: 13px;
            color: var(--text-light);
            margin-bottom: 20px;
            padding-right: 5px;
        }

        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 25px;
        }

        .style-buttons {
            display: flex;
            justify-content: space-around; /* 改为均匀分布 */
            flex-wrap: wrap;
            gap: 15px; /* 增加间距 */
            margin-bottom: 30px; /* 增加与下一组按钮的间距 */
        }

        .style-buttons button {
            flex: 0 1 auto;
            min-width: 90px;
            padding: 10px 15px; /* 增加内边距 */
            font-size: 14px;
            text-align: center;
            border-radius: 6px; /* 增加圆角 */
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 10px;
            margin-bottom: 30px;
        }

        button {
            padding: 10px 18px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        button:focus {
            outline: none;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
        }

        .btn-primary:disabled {
            background-color: #a0a0a0;
            cursor: not-allowed;
        }

        .btn-secondary {
            background-color: white;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }

        .btn-secondary:hover {
            background-color: rgba(74, 107, 175, 0.1);
        }

        .btn-secondary:disabled {
            color: #a0a0a0;
            border-color: #a0a0a0;
            cursor: not-allowed;
        }

        .btn-danger {
            background-color: white;
            color: var(--error-color);
            border: 1px solid var(--error-color);
        }

        .btn-danger:hover {
            background-color: rgba(244, 67, 54, 0.1);
        }

        .btn-icon {
            margin-right: 6px;
        }

        .loading {
            display: none;
            color: var(--text-light);
            font-style: italic;
            margin: 10px 0;
            padding: 10px;
            background-color: rgba(0, 0, 0, 0.03);
            border-radius: 4px;
            text-align: center;
        }

        .loading::before {
            content: "";
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid var(--primary-light);
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
            margin-right: 8px;
            vertical-align: middle;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .result-box {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            min-height: 100px;
            margin-top: 20px;
            white-space: pre-wrap;
            background-color: white;
            transition: var(--transition);
            font-size: 15px;
            line-height: 1.6;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .result-box.success {
            border-left: 4px solid var(--success-color);
        }

        .result-box.error {
            border-left: 4px solid var(--error-color);
            color: var(--error-color);
        }

        .status-message {
            margin-top: 10px;
            font-size: 12px;
            height: 16px;
            transition: var(--transition);
        }

        .status-success {
            color: var(--success-color);
        }

        .status-error {
            color: var(--error-color);
        }

        /* 字数设置对话框样式 */
        .custom-dialog {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .custom-dialog-content {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            width: 90%;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .custom-dialog-content h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: var(--primary-color);
            font-size: 18px;
        }

        .dialog-body {
            margin-bottom: 20px;
        }

        .input-group {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .input-group label {
            flex: 0 0 80px;
            font-weight: 500;
        }

        .input-group input {
            flex: 1;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }

        .dialog-message {
            font-size: 13px;
            color: #666;
            margin-top: 10px;
        }

        .dialog-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        /* 响应式设计 */
        @media (max-width: 600px) {
            .container {
                padding: 10px;
            }

            .card {
                padding: 15px;
            }

            .button-group {
                flex-direction: column;
                gap: 8px;
            }

            button {
                width: 100%;
            }
        }
    </style>
    <!-- 引入ribbon.js文件 -->
    <script src="../js/ribbon.js"></script>
</head>

<body>
    <div class="container">
        <div class="card">
            <div class="header">
                <span class="header-icon">✨</span>
                <h2>文本润色</h2>
            </div>

            <textarea id="inputText" oninput="updateCharCount()" placeholder="请输入需要润色的文本内容..."></textarea>
            <div id="charCount" class="char-count">字符数: 0</div>

            <!-- 第一行按钮：不同的润色风格 -->
            <div class="button-group style-buttons">
                <button class="btn-primary" onclick="polishText('polish')" id="polishBtn">
                    <span class="btn-icon">✨</span>润色
                </button>
                <button class="btn-primary" onclick="polishText('political')" id="politicalBtn">
                    <span class="btn-icon">🇨🇳</span>党政风
                </button>
                <button class="btn-primary" onclick="polishText('condense')" id="condenseBtn">
                    <span class="btn-icon">🔍</span>缩写
                </button>
                <button class="btn-primary" onclick="polishText('expand')" id="expandBtn">
                    <span class="btn-icon">🔎</span>扩写
                </button>
            </div>

            <!-- 字数设置对话框 -->
            <div id="wordCountDialog" class="custom-dialog" style="display: none;">
                <div class="custom-dialog-content">
                    <h3 id="dialogTitle">设置字数</h3>
                    <div class="dialog-body">
                        <div class="input-group">
                            <label for="targetWordCount">目标字数：</label>
                            <input type="number" id="targetWordCount" max="10000" />
                        </div>
                        <div class="dialog-message" id="dialogMessage">
                            请输入目标字数，或使用默认设置。
                        </div>
                    </div>
                    <div class="dialog-buttons">
                        <button onclick="closeWordCountDialog()" class="btn-secondary">取消</button>
                        <button onclick="useDefaultWordCount()" class="btn-secondary">默认设置</button>
                        <button onclick="applyWordCount()" class="btn-primary">确定</button>
                    </div>
                </div>
            </div>

            <!-- 第二行按钮：操作按钮 -->
            <div class="button-group action-buttons">
                <button class="btn-secondary" onclick="copyPolishedText()" id="copyBtn" disabled>
                    <span class="btn-icon">📋</span>复制结果
                </button>
                <button class="btn-secondary" onclick="replaceOriginalText()" id="replaceBtn" disabled>
                    <span class="btn-icon">📝</span>替换原文
                </button>
                <button class="btn-danger" onclick="clearAll()">
                    <span class="btn-icon">🗑️</span>清空
                </button>
            </div>

            <div id="loading" class="loading">正在润色中，请稍候...</div>
            <div id="polishedText" class="result-box"></div>
            <div id="statusMessage" class="status-message"></div>
        </div>
    </div>

    <script>
        // 页面加载时初始化文本润色功能
        window.onload = function() {
            // 调用ribbon.js中的初始化函数
            initTextPolishFeature();

            // 解析URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const action = urlParams.get('action');
            const text = urlParams.get('text');

            // 如果有参数，自动执行相应功能
            if (action && text) {
                // 解码文本（URL参数会被编码）
                const decodedText = decodeURIComponent(text);

                // 设置输入文本
                document.getElementById('inputText').value = decodedText;
                // 更新字符计数
                updateCharCount();

                // 根据不同的操作执行相应的功能
                setTimeout(() => {
                    switch(action) {
                        case 'polish':
                            polishText('polish');
                            break;
                        case 'expand':
                            polishText('expand');
                            break;
                        case 'condense':
                            polishText('condense');
                            break;
                        case 'political':
                            polishText('political');
                            break;
                    }
                }, 500); // 给页面元素加载的时间
            }
        };
    </script>
</body>
</html>