<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模板管理</title>
    <link rel="stylesheet" href="../css/template-modern.css">
    <!-- 引入ribbon.js文件 -->
    <script src="../js/ribbon.js"></script>
    <!-- 引入模板管理功能的JavaScript文件 -->
    <script src="../js/template.js"></script>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>模板管理</h1>
        </div>

        <div class="template-container">
            <!-- 搜索和筛选 -->
            <div class="search-filter-container">
                <div class="search-box">
                    <input type="text" id="searchTemplate" placeholder="搜索模板...">
                </div>
                <div class="filter-box">
                    <select id="categoryFilter">
                        <option value="all">全部分类</option>
                        <!-- 其他分类选项将由JavaScript动态添加 -->
                    </select>
                </div>
                <button class="btn btn-primary create-template-btn" onclick="showCreateTemplateDialog()" id="createTemplateBtn">
                    <span class="btn-icon">✚</span>创建模板
                </button>
            </div>

            <!-- 模板列表 -->
            <div id="templateList" class="template-list">
                <!-- 模板卡片将通过JavaScript动态生成 -->
                <div class="no-templates">加载模板中...</div>
            </div>

            <!-- 状态消息 -->
            <div id="statusMessage" class="status-message"></div>
        </div>
    </div>

    <!-- 创建模板对话框 -->
    <div id="createTemplateDialog" class="dialog">
        <div class="dialog-content">
            <div class="dialog-header">
                <h3 class="dialog-title">创建新模板</h3>
                <button class="dialog-close" onclick="closeCreateTemplateDialog()">×</button>
            </div>
            <div class="dialog-body">
                <div class="form-group">
                    <label for="templateName">模板名称 *</label>
                    <input type="text" id="templateName" placeholder="输入模板名称" required>
                </div>
                <div class="form-group">
                    <label for="templateCategory">分类</label>
                    <select id="templateCategory">
                        <option value="公文">公文</option>
                        <option value="报告">报告</option>
                        <option value="会议">会议</option>
                        <option value="学术论文">学术论文</option>
                        <option value="商业文档">商业文档</option>
                        <option value="法律文书">法律文书</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="templateDescription">描述</label>
                    <input type="text" id="templateDescription" placeholder="输入模板描述（可选）">
                </div>
                <div class="form-group">
                    <label for="templateContent">模板内容 *</label>
                    <textarea id="templateContent" placeholder="输入模板内容，可以使用【占位符】表示需要填写的内容" required></textarea>
                </div>
            </div>
            <div class="dialog-footer">
                <button class="btn btn-secondary" onclick="closeCreateTemplateDialog()">取消</button>
                <button class="btn btn-primary" onclick="saveNewTemplate()">保存</button>
            </div>
        </div>
    </div>

    <!-- 预览模板对话框 -->
    <div id="previewTemplateDialog" class="dialog">
        <div class="dialog-content">
            <div class="dialog-header">
                <h3 class="dialog-title" id="previewTemplateTitle">模板预览</h3>
                <button class="dialog-close" onclick="closePreviewDialog()">×</button>
            </div>
            <div class="dialog-body">
                <div class="preview-content" id="previewTemplateContent"></div>
            </div>
            <div class="dialog-footer">
                <button class="btn btn-secondary" onclick="closePreviewDialog()">关闭</button>
            </div>
        </div>
    </div>

    <!-- 编辑模板格式对话框 -->
    <div id="editFormatDialog" class="dialog">
        <div class="dialog-content">
            <div class="dialog-header">
                <h3 class="dialog-title" id="editFormatTitle">编辑模板格式</h3>
                <button class="dialog-close" onclick="closeEditFormatDialog()">×</button>
            </div>
            <div class="dialog-body">
                <!-- 隐藏的模板ID输入框 -->
                <input type="hidden" id="editTemplateId">

                <!-- 格式化工具栏 -->
                <div class="format-toolbar">
                    <button class="format-btn" onclick="applyFormatTool('bold')" title="加粗文本（使用【】包围）">
                        <span class="format-icon">【】</span>
                    </button>
                    <button class="format-btn" onclick="applyFormatTool('indent')" title="缩进选中文本">
                        <span class="format-icon">→→</span>
                    </button>
                    <button class="format-btn" onclick="applyFormatTool('indent-all')" title="所有段落缩进">
                        <span class="format-icon">⇥</span>
                    </button>
                    <button class="format-btn" onclick="applyFormatTool('remove-blank-lines')" title="移除空行">
                        <span class="format-icon">␣✕</span>
                    </button>
                    <button class="format-btn" onclick="applyFormatTool('add-placeholder')" title="添加占位符">
                        <span class="format-icon">【□】</span>
                    </button>
                    <button class="format-btn" onclick="applyFormatTool('add-title')" title="添加标题格式">
                        <span class="format-icon">标题</span>
                    </button>
                </div>

                <div class="form-group">
                    <label for="editFormatContent">模板内容 *</label>
                    <textarea id="editFormatContent" class="format-editor" placeholder="编辑模板内容，可以使用上方的格式化工具" required></textarea>
                </div>
                <div class="format-tips">
                    <p><strong>操作提示：</strong></p>
                    <ul>
                        <li>选中文本后点击格式化工具按钮应用格式</li>
                        <li>使用【】包围文本表示需要填写的内容</li>
                        <li>修改格式后将直接更新原模板内容</li>
                    </ul>
                </div>
            </div>
            <div class="dialog-footer">
                <button class="btn btn-secondary" onclick="closeEditFormatDialog()">取消</button>
                <button class="btn btn-primary" onclick="saveTemplateFormat()">保存</button>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时初始化模板管理功能
        window.onload = function() {
            // 调用template.js中的初始化函数
            initTemplateFeature();
        };
    </script>
</body>

</html>
