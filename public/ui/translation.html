<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语言翻译</title>
    <link rel="stylesheet" href="../css/translation-modern.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>语言翻译</h1>
        </div>

        <div class="translation-container">
            <!-- 输入区域 -->
            <div class="input-section">
                <div class="section-header">
                    <h2>原始文本</h2>
                    <div id="charCount" class="char-count">字符数: 0</div>
                </div>
                <textarea id="inputText" class="text-area" placeholder="请输入需要翻译的文本内容..." oninput="updateTranslationCharCount()"></textarea>
            </div>

            <!-- 翻译语言选择按钮 -->
            <div class="language-buttons">
                <button class="lang-btn zh-to-en" onclick="translateText('zh', 'en')" id="zhToEnBtn">
                    <span class="btn-icon">🇨🇳➡️🇬🇧</span>
                    <span class="btn-text">中译英</span>
                </button>
                <button class="lang-btn en-to-zh" onclick="translateText('en', 'zh')" id="enToZhBtn">
                    <span class="btn-icon">🇬🇧➡️🇨🇳</span>
                    <span class="btn-text">英译中</span>
                </button>
                <button class="lang-btn custom-lang" onclick="showCustomLanguageDialog()" id="customLangBtn">
                    <span class="btn-icon">🌐</span>
                    <span class="btn-text">自定义语言</span>
                </button>
                <button class="lang-btn clear-btn" onclick="clearTranslation()" id="clearBtn">
                    <span class="btn-icon">🗑️</span>
                    <span class="btn-text">清空</span>
                </button>
            </div>

            <!-- 输出区域 -->
            <div class="output-section">
                <div class="section-header">
                    <h2>翻译结果</h2>
                    <div class="action-buttons">
                        <button class="action-btn" id="copyBtn" onclick="copyTranslatedText()" disabled>
                            <span class="btn-icon">📋</span>
                            <span class="btn-text">复制</span>
                        </button>
                        <button class="action-btn" id="replaceBtn" onclick="replaceWithTranslation()" disabled>
                            <span class="btn-icon">📝</span>
                            <span class="btn-text">替换原文</span>
                        </button>
                    </div>
                </div>

                <!-- 加载指示器 -->
                <div id="loading" class="loading" style="display: none;">
                    <div class="loading-dots">
                        <div class="loading-dot"></div>
                        <div class="loading-dot"></div>
                        <div class="loading-dot"></div>
                    </div>
                </div>


                <!-- 状态消息 - 隐藏 -->
                <div id="statusMessage" class="status-message" style="display: none;"></div>



                <!-- 翻译结果显示区域 -->
                <div id="translatedText" class="result-box"></div>
            </div>
        </div>
    </div>

    <!-- 自定义语言对话框 -->
    <div id="customLangDialog" class="custom-lang-dialog" style="display: none;">
        <div class="custom-lang-content">
            <h3>自定义语言翻译</h3>
            <div class="lang-select-container">
                <div class="lang-select">
                    <label for="sourceLang">源语言：</label>
                    <select id="sourceLang">
                        <option value="zh" selected>中文</option>
                        <option value="en">英文</option>
                        <option value="ja">日文</option>
                        <option value="ko">韩文</option>
                        <option value="fr">法文</option>
                        <option value="de">德文</option>
                        <option value="es">西班牙文</option>
                        <option value="ru">俄文</option>
                    </select>
                </div>
                <div class="lang-arrow">➡️</div>
                <div class="lang-select">
                    <label for="targetLang">目标语言：</label>
                    <select id="targetLang">
                        <option value="zh">中文</option>
                        <option value="en" selected>英文</option>
                        <option value="ja">日文</option>
                        <option value="ko">韩文</option>
                        <option value="fr">法文</option>
                        <option value="de">德文</option>
                        <option value="es">西班牙文</option>
                        <option value="ru">俄文</option>
                    </select>
                </div>
            </div>
            <div class="dialog-buttons">
                <button onclick="closeCustomLanguageDialog()" class="btn-secondary">取消</button>
                <button onclick="translateWithCustomLanguage()" class="btn-primary">翻译</button>
            </div>
        </div>
    </div>

    <!-- 引入 JavaScript 文件 -->
    <script src="../js/ribbon.js"></script>
    <script src="../js/translation.js"></script>
    <script>
        // 页面加载时初始化翻译功能
        window.onload = function() {
            // 调用translation.js中的初始化函数
            initTranslationFeature();

            // 解析URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const action = urlParams.get('action');
            const text = urlParams.get('text');
            const sourceLang = urlParams.get('sourceLang');
            const targetLang = urlParams.get('targetLang');

            // 如果有参数，自动执行相应功能
            if (action && text) {
                // 解码文本（URL参数会被编码）
                const decodedText = decodeURIComponent(text);

                // 检查文本长度是否超过2个字符
                if (decodedText.trim().length > 2) {
                    // 设置输入文本
                    document.getElementById('inputText').value = decodedText;
                    // 更新字符计数
                    updateTranslationCharCount();

                    // 根据不同的操作执行相应的功能
                    setTimeout(() => {
                        if (action === 'translate' && sourceLang && targetLang) {
                            // 执行翻译
                            translateText(sourceLang, targetLang);
                        } else if (action === 'customLang') {
                            // 显示自定义语言对话框
                            showCustomLanguageDialog();
                        }
                    }, 500); // 给页面元素加载的时间
                }
            }

        };
    </script>
</body>
</html>
