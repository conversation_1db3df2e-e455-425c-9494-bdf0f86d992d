<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专利申请技术交底书生成</title>
    <style>
        /* 专利申请技术交底书生成现代化UI样式 */
        :root {
            --primary-color: #4568dc;
            --primary-gradient: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%);
            --secondary-color: #6c757d;
            --success-color: #10b981;
            --info-color: #3b82f6;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --light-color: #f8fafc;
            --dark-color: #1e293b;
            --border-radius: 12px;
            --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            --transition: all 0.3s ease;
            --font-family: "Microsoft YaHei", "微软雅黑", -apple-system, BlinkMacSystemFont, "<PERSON><PERSON><PERSON> UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: var(--font-family);
        }

        body {
            background-color: #f8fafc;
            color: #334155;
            padding: 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .container {
            max-width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: #fff;
            box-shadow: var(--box-shadow);
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        /* 现代化标题栏 */
        .header {
            background: var(--primary-gradient);
            color: white;
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 10;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
        }

        .header h1::before {
            content: "📝";
            display: inline-block;
            margin-right: 8px;
            font-size: 22px;
        }

        /* 主容器 */
        .patent-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            overflow: hidden;
            background-color: #f8fafc;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(69, 104, 220, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(176, 106, 179, 0.05) 0%, transparent 50%);
            overflow-y: auto;
        }

        /* 表单容器 */
        .form-container {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
            margin-top: 20px; /* 添加顶部边距，弥补移除模板说明框后的空间 */
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #334155;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: var(--transition);
            background-color: white;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(69, 104, 220, 0.1);
        }

        .button-container {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .generate-button {
            background: var(--primary-gradient);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 25px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: 0 2px 10px rgba(69, 104, 220, 0.2);
            display: flex;
            align-items: center;
        }

        .generate-button::before {
            content: "✨";
            margin-right: 8px;
            font-size: 18px;
        }

        .generate-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(69, 104, 220, 0.3);
        }

        .generate-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 加载指示器 */
        .loading-indicator {
            display: none;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            padding: 15px;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
        }

        .loading-dots {
            display: flex;
            gap: 6px;
        }

        .loading-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--primary-gradient);
            animation: dot-pulse 1.5s infinite ease-in-out;
        }

        .loading-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .loading-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes dot-pulse {
            0%, 100% { transform: scale(0.8); opacity: 0.6; }
            50% { transform: scale(1.2); opacity: 1; }
        }

        /* 进度条 */
        .progress-container {
            height: 4px;
            background-color: #e2e8f0;
            border-radius: 2px;
            overflow: hidden;
            margin: 10px 0;
            display: none;
        }

        .progress-bar {
            height: 100%;
            background: var(--primary-gradient);
            width: 0%;
            transition: width 0.3s ease;
        }

        /* 状态消息 */
        .status-message {
            font-size: 14px;
            padding: 10px;
            border-radius: var(--border-radius);
            text-align: center;
            margin: 10px 0;
            display: none;
        }

        .status-success {
            background-color: #ecfdf5;
            color: var(--success-color);
            border: 1px solid #a7f3d0;
        }

        .status-error {
            background-color: #fef2f2;
            color: var(--danger-color);
            border: 1px solid #fecaca;
        }

        /* 思考过程容器 */
        .thinking-container {
            background-color: #f8f9fa;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
            display: none;
        }

        .thinking-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #64748b;
            display: flex;
            align-items: center;
        }

        .thinking-title::before {
            content: "💭";
            margin-right: 8px;
            font-size: 20px;
        }

        .thinking-content {
            background-color: #f1f5f9;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            line-height: 1.6;
            font-size: 14px;
            color: #64748b;
            font-family: monospace;
        }

        /* 结果容器 */
        .result-container {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
            display: none;
        }

        .result-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--primary-color);
            display: flex;
            align-items: center;
        }

        .result-title::before {
            content: "📄";
            margin-right: 8px;
            font-size: 20px;
        }

        .result-content {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            line-height: 1.6;
            font-size: 14px;
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            gap: 15px;
        }

        .action-button {
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-button.insert {
            background-color: var(--success-color);
            color: white;
            border: none;
        }

        .action-button.insert::before {
            content: "📋";
            margin-right: 8px;
        }

        .action-button.copy {
            background-color: var(--info-color);
            color: white;
            border: none;
        }

        .action-button.copy::before {
            content: "📝";
            margin-right: 8px;
        }

        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .action-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .patent-container {
                padding: 15px;
            }

            .header h1 {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>专利申请技术交底书生成</h1>
        </div>
        <div class="patent-container">
            <div class="form-container">
                <div class="form-group">
                    <label for="projectName">项目名称：</label>
                    <input type="text" id="projectName" placeholder="请输入专利项目名称，如：基于深度学习的图像识别系统" required>
                </div>
                <div class="button-container">
                    <button id="generateButton" class="generate-button">生成专利申请技术交底书</button>
                </div>
            </div>
            <div id="loadingIndicator" class="loading-indicator">
                <div class="loading-dots">
                    <div class="loading-dot"></div>
                    <div class="loading-dot"></div>
                    <div class="loading-dot"></div>
                </div>
                <span style="margin-left: 10px;">正在生成中，请稍候...</span>
            </div>
            <div id="progressContainer" class="progress-container">
                <div id="progressBar" class="progress-bar"></div>
            </div>
            <div id="statusMessage" class="status-message"></div>
            <!-- 思考过程容器 -->
            <div id="thinkingContainer" class="thinking-container">
                <div class="thinking-title">思考过程</div>
                <div id="thinkingContent" class="thinking-content"></div>
            </div>

            <div id="resultContainer" class="result-container">
                <div class="result-title">生成结果</div>
                <div id="resultContent" class="result-content"></div>
                <div class="action-buttons">
                    <button id="insertButton" class="action-button insert">插入到文档</button>
                    <button id="copyButton" class="action-button copy">复制内容</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引用专利申请技术交底书生成功能的JS文件 -->
    <script src="../js/patent.js"></script>
</body>
</html>
