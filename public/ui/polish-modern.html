<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文本润色</title>
    <link rel="stylesheet" href="../css/polish-modern.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>文本润色</h1>
        </div>

        <div class="polish-container">
            <!-- 输入区域 -->
            <div class="input-section">
                <div class="section-header">
                    <h2>原始文本</h2>
                    <div id="charCount" class="char-count">字符数: 0</div>
                </div>
                <textarea id="inputText" class="text-area" placeholder="请输入需要润色的文本内容..." oninput="updateCharCount()"></textarea>
            </div>

            <!-- 功能按钮区域 -->
            <div class="function-buttons">
                <button class="function-btn polish-btn" onclick="polishText('polish')" id="polishBtn">
                    <span class="btn-icon">✨</span>
                    <span class="btn-text">润色</span>
                </button>
                <button class="function-btn political-btn" onclick="polishText('political')" id="politicalBtn">
                    <span class="btn-icon">🏛️</span>
                    <span class="btn-text">党政风</span>
                </button>
                <button class="function-btn condense-btn" onclick="polishText('condense')" id="condenseBtn">
                    <span class="btn-icon">🔍</span>
                    <span class="btn-text">缩写</span>
                </button>
                <button class="function-btn expand-btn" onclick="polishText('expand')" id="expandBtn">
                    <span class="btn-icon">🔎</span>
                    <span class="btn-text">扩写</span>
                </button>
                <button class="function-btn clear-btn" onclick="clearAll()" id="clearBtn">
                    <span class="btn-icon">🗑️</span>
                    <span class="btn-text">清空</span>
                </button>
            </div>

            <!-- 输出区域 -->
            <div class="output-section">
                <div class="section-header">
                    <h2>润色结果</h2>
                    <div class="action-buttons">
                        <button class="action-btn" id="copyBtn" onclick="copyPolishedText()" disabled>
                            <span class="btn-icon">📋</span>
                            <span class="btn-text">复制</span>
                        </button>
                        <button class="action-btn" id="replaceBtn" onclick="replaceOriginalText()" disabled>
                            <span class="btn-icon">📝</span>
                            <span class="btn-text">替换原文</span>
                        </button>
                    </div>
                </div>

                <!-- 加载指示器 -->
                <div id="loading" class="loading" style="display: none;">
                    <div class="loading-dots">
                        <div class="loading-dot"></div>
                        <div class="loading-dot"></div>
                        <div class="loading-dot"></div>
                    </div>
                </div>

                <!-- 进度条 -->
                <div id="progressContainer" class="progress-container" style="display: none;">
                    <div id="progressBar" class="progress-bar"></div>
                </div>

                <!-- 状态消息 -->
                <div id="statusMessage" class="status-message"></div>

                <!-- 思考过程显示区域 -->
                <div id="thinkingProcess" class="thinking-process" style="display: none;">
                    <div class="thinking-header">
                        <span>思考过程</span>
                    </div>
                    <div class="thinking-content"></div>
                </div>

                <!-- 润色结果显示区域 -->
                <div id="polishedText" class="result-box"></div>
            </div>
        </div>
    </div>

    <!-- 引入 JavaScript 文件 -->
    <script src="../js/ribbon.js"></script>
    <script>
        // 页面加载时初始化文本润色功能
        window.onload = function() {
            // 调用ribbon.js中的初始化函数
            initTextPolishFeature();

            // 解析URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const action = urlParams.get('action');
            const text = urlParams.get('text');

            // 如果有参数，自动执行相应功能
            if (action && text) {
                // 解码文本（URL参数会被编码）
                const decodedText = decodeURIComponent(text);

                // 设置输入文本
                document.getElementById('inputText').value = decodedText;
                // 更新字符计数
                updateCharCount();

                // 根据不同的操作执行相应的功能
                setTimeout(() => {
                    switch(action) {
                        case 'polish':
                            polishText('polish');
                            break;
                        case 'expand':
                            polishText('expand');
                            break;
                        case 'condense':
                            polishText('condense');
                            break;
                        case 'political':
                            polishText('political');
                            break;
                    }
                }, 500); // 给页面元素加载的时间
            }

        };
    </script>
</body>
</html>
