/**
 * WPS加载项相关JavaScript函数
 * 用于西部钻探AI插件的安装和管理
 */

// 全局变量
var serverId = getServerId();
var serverVersion = "wait";

/**
 * 获取HTTP对象
 */
function getHttpObj() {
    var httpobj = null;
    if (IEVersion() < 10) {
        try {
            httpobj = new XDomainRequest();
        } catch (e1) {
            httpobj = new createXHR();
        }
    } else {
        httpobj = new createXHR();
    }
    return httpobj;
}

/**
 * 兼容IE低版本的创建xmlhttprequest对象的方法
 */
function createXHR() {
    if (typeof XMLHttpRequest != 'undefined') { //兼容高版本浏览器
        return new XMLHttpRequest();
    } else if (typeof ActiveXObject != 'undefined') { //IE6 采用 ActiveXObject
        var versions = [ //由于MSXML库有3个版本，因此都要考虑
            'MSXML2.XMLHttp.6.0',
            'MSXML2.XMLHttp.3.0',
            'MSXML2.XMLHttp'
        ];

        for (var i = 0; i < versions.length; i++) {
            try {
                return new ActiveXObject(versions[i]);
            } catch (e) {
                //跳过
            }
        }
    } else {
        throw new Error('您的浏览器不支持XHR对象');
    }
}

/**
 * 检测IE版本
 */
function IEVersion() {
    var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
    var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器
    var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器
    var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
    if (isIE) {
        var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
        reIE.test(userAgent);
        var fIEVersion = parseFloat(RegExp["$1"]);
        if (fIEVersion == 7) {
            return 7;
        } else if (fIEVersion == 8) {
            return 8;
        } else if (fIEVersion == 9) {
            return 9;
        } else if (fIEVersion == 10) {
            return 10;
        } else {
            return 6; //IE版本<=7
        }
    } else if (isEdge) {
        return 20; //edge
    } else if (isIE11) {
        return 11; //IE11
    } else {
        return 30; //不是ie浏览器
    }
}

/**
 * 生成guid的接口
 * @returns guid
 */
function guid() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

/**
 * 获取serverId的接口
 * @returns serverId
 */
function getServerId() {
    if (window.localStorage) {
        if (localStorage.getItem("serverId")) {
            //
        }
        else {
            localStorage.setItem("serverId", guid());
        }
        return localStorage.getItem("serverId");
    }
    else {
        return guid();
    }
}

/**
 * 自定义协议启动服务端
 */
function InitWpsCloudSvr() {
    if (serverId == undefined)
        window.location.href = "ksoWPSCloudSvr://start=RelayHttpServer"//是否启动wps弹框
    else
        window.location.href = "ksoWPSCloudSvr://start=RelayHttpServer" + "&serverId=" + serverId //是否启动wps弹框
}

/**
 * 启动WPS
 */
function startWps(req, t, callback) {
    function startWpsInnder(reqInner, tryCount, bPop) {
        if (tryCount < 1) {
            if (callback)
                callback({
                    status: 2,
                    message: "请允许浏览器打开WPS Office"
                });
            return;
        }
        var bRetry = true;
        var xmlReq = getHttpObj();
        xmlReq.open(reqInner.type, reqInner.url);
        xmlReq.onload = function (res) {
            if (res.target.status != 200) {
                var responseStr = IEVersion() < 10 ? xmlReq.responseText : res.target.response;
                var errorMessage = JSON.parse(responseStr)
                if (errorMessage.data == "Subserver not available." && tryCount == 4 && bPop) {
                    InitWpsCloudSvr();
                    setTimeout(function () {
                        if (bRetry) {
                            bRetry = false;
                            startWpsInnder(reqInner, --tryCount, false);
                        }
                    }, 3000);
                }
            }
            if (callback)
                callback({
                    status: 0,
                    res: res
                });
        }
        xmlReq.ontimeout = xmlReq.onerror = function (res) {
            xmlReq.bTimeout = true;
            if (bPop) { //打开wps并传参
                InitWpsCloudSvr()
            }
            setTimeout(function () {
                if (bRetry) {
                    bRetry = false;
                    startWpsInnder(reqInner, --tryCount, false);
                }
            }, 1000);
        }
        if (IEVersion() < 10) {
            xmlReq.onreadystatechange = function () {
                if (xmlReq.readyState != 4)
                    return;
                if (xmlReq.bTimeout) {
                    return;
                }
                if (xmlReq.status === 200)
                    xmlReq.onload();
                else
                    xmlReq.onerror();
            }
        }
        xmlReq.timeout = 3000;
        xmlReq.send(t)
    }
    startWpsInnder(req, 4, true);
    return;
}

/**
 * 获取加载项ID
 */
function GetAddonId(element) {
    return element.name + "/" + element.addonType;
}

/**
 * 检查插件状态
 */
function CheckPlugin(element) {
    var id = GetAddonId(element);
    var ele = document.getElementById(id + "_status");
    var xmlReq = getHttpObj();
    var offline = element.online === "false";
    var url = offline ? element.url : element.url + "ribbon.xml";
    xmlReq.open("POST", "http://127.0.0.1:58890/redirect/runParams");
    xmlReq.onload = function (res) {
        if ((offline && res.target.response.startsWith("7z"))
            || !offline && res.target.response.startsWith("<customUI")) {
            ele.style.color = "green";
            ele.style.textAlign = "center";
            ele.innerHTML = "正常";
        } else {
            ele.style.color = "white";
            ele.style.backgroundColor = "gray";
            ele.style.textAlign = "center";
            ele.innerHTML = "无效";
            ele.title = offline ? ("不是有效的7z格式" + url) : ("不是有效的ribbon.xml，" + url);
        }
    }
    xmlReq.onerror = function (res) {
        xmlReq.bTimeout = true;
        ele.style.color = "white";
        ele.style.backgroundColor = "gray";
        ele.style.textAlign = "center";
        ele.innerHTML = "无效";
        ele.title = "网页路径不可访问，如果是跨域问题，不影响使用：" + url;
    }
    xmlReq.ontimeout = function (res) {
        xmlReq.bTimeout = true;
        ele.style.color = "white";
        ele.style.backgroundColor = "gray";
        ele.style.textAlign = "center";
        ele.innerHTML = "异常";
        ele.title = "访问超时，" + url;
    }
    if (IEVersion() < 10) {
        xmlReq.onreadystatechange = function () {
            if (xmlReq.readyState != 4)
                return;
            if (xmlReq.bTimeout) {
                return;
            }
            if (xmlReq.status === 200)
                xmlReq.onload();
            else
                xmlReq.onerror();
        }
    }
    xmlReq.timeout = 5000;
    var data = {
        method: "get",
        url: url,
        data: ""
    }
    var sendData = FormatSendData(data)
    xmlReq.send(sendData);
}

/**
 * 更新元素
 */
function UpdateElement(element, cmd) {
    if (typeof element.name === 'undefined')
        return
    var id = GetAddonId(element);
    var addonList = document.getElementById("addonList");
    var buttonLabel = cmd === 'enable' ? "安装" : "卸载";
    var des = "文字";
    if (element.addonType == "et")
        des = "电子表格";
    else if (element.addonType == "wpp")
        des = "演示";
    var loadType = "在线";
    if (element.online == "false")
        loadType = "离线";
    var old = document.getElementById(id);
    if (old !== null) {
        var oldOnline = old.wpsaddon.online === "false";
        var newOnline = element.online === "false";
        if (cmd === 'disable'
            && (oldOnline !== newOnline
                || old.wpsaddon.url !== element.url
                || (oldOnline && old.wpsaddon.version !== element.version))) {
            buttonLabel = "更新/卸载";
            cmd = "choose";
        }
        old.wpsaddoncmd = cmd;
        document.getElementById(id + '_button').innerHTML = buttonLabel;
        document.getElementById(id + '_domain').innerHTML = element.customDomain;
    } else {
        var ele = document.createElement("div");
        ele.className = "addonItem";
        ele.id = id;
        ele.wpsaddon = element;
        ele.wpsaddoncmd = cmd;
        ele.innerHTML =
            '<div class="addonItemName1">' + element.name + '</div>\n' +
            '<div class="addonItemName2">' + des + '</div>\n' +
            '<div class="addonItemName3">' + loadType + '</div>\n' +
            '<div class="addonItemName4">' + element.url + '</div>\n' +
            '<div class="addonItemName1" id="' + id + '_domain' + '">' + element.customDomain + '</div>\n' +
            '<div class="addonItemName5"><div class="addonItemButton" id="' + id + '_button' + '" onclick="WpsAddonHandle(\'' + id + '\')">' + buttonLabel + '</div></div>\n' +
            '<div class="addonItemName6" id="' + id + '_status' + '">验证中...</div>\n';
        addonList.appendChild(ele);
        CheckPlugin(element);
    }
}

/**
 * 处理WPS加载项
 */
function WpsAddonHandle(id) {
    var ele = document.getElementById(id);
    var element = ele.wpsaddon;
    var cmd = ele.wpsaddoncmd;
    WpsAddonHandleEx(element, cmd)
}

/**
 * 处理WPS加载项扩展
 */
function WpsAddonHandleEx(element, cmd) {
    // 保存原始命令，用于后续显示正确的提示信息
    var originalCmd = cmd;

    if (cmd === "choose") {
        if (confirm("点击确定将更新 WPS 加载项，或点击取消完成卸载")) {
            cmd = "enable";
            originalCmd = "update"; // 标记为更新操作
        } else {
            cmd = "disable";
        }
    }
    var data = FormartData(element, cmd);
    var req = { url: "http://127.0.0.1:58890/deployaddons/runParams", type: "POST" };
    startWps(req, data, function (res) {
        if (res.status == 0) {
            if (cmd === "disableall") {
                window.location.reload();
            } else {
                if (res.res.target.response == "OK"
                    || (res.res.target.response == "" && res.res.target.status == 200)) {
                    var newCmd = 'disable';
                    var successMessage = "安装成功！";

                    if (cmd === 'disable') {
                        newCmd = 'enable';
                        successMessage = "卸载成功！";
                    } else if (cmd === 'enable') {
                        if (originalCmd === 'update') {
                            successMessage = "更新成功！";
                        } else {
                            successMessage = "安装成功！";
                        }
                    }

                    UpdateElement(element, newCmd);
                    alert(successMessage);
                }
                else {
                    alert("配置失败！");
                }
            }
        } else {
            alert(res.message);
        }
    });
}

/**
 * 格式化数据
 */
function FormartData(element, cmd) {
    var data = {
        "cmd": cmd, //"enable", 启用， "disable", 禁用, "disableall", 禁用所有
        "name": element.name,
        "url": element.url,
        "addonType": element.addonType,
        "online": element.online,
        "version": element.version,
        "customDomain": element.customDomain
    }
    return FormatSendData(data);
}

/**
 * 格式化发送数据
 */
function FormatSendData(data) {
    var strData = JSON.stringify(data);
    if (IEVersion() < 10)
        eval("strData = '" + JSON.stringify(strData) + "';");

    if (serverVersion >= "1.0.2" && serverId != undefined) {
        var base64Data = encode(strData);
        return JSON.stringify({
            serverId: serverId,
            data: base64Data
        })
    }
    else {
        return encode(strData);
    }
}

/**
 * 加载本地加载项
 */
function LoadLocalAddons() {
    var baseData
    if (serverVersion >= "1.0.2" && serverId != undefined)
        baseData = JSON.stringify({ serverId: serverId });
    var req = { url: "http://127.0.0.1:58890/publishlist", type: "POST" };
    startWps(req, baseData, function (res) {
        if (res.status == 0) {
            var addonList = document.getElementById("addonList");
            var curList = JSON.parse(res.res.target.response);
            curList.forEach(function (element) {
                if (element.enable === "false")
                    return;
                UpdateElement(element, 'disable')
            });
        } else {
            console.log(res.message);
        }
    });
}

/**
 * 加载发布加载项
 */
function LoadPublishAddons() {
    var addonList = document.getElementById("addonList");
    var curList = [
        {"name":"xdecai","addonType":"wps","online":"true","multiUser":"true","url":"http://10.142.36.50:3000/"}
    ];
    curList.forEach(function (element) {
        var param = JSON.stringify(element).replace("\"", "\'");
        UpdateElement(element, 'enable')
    });
}

/**
 * 初始化SDK
 */
function InitSdk() {
    var req = { url: "http://127.0.0.1:58890/version", type: "POST" };
    startWps(
        req,
        JSON.stringify({ serverId: serverId }),
        function (res) {
            if (res.status !== 0) {
                return;
            }
            if (serverVersion == "wait") {
                serverVersion = res.res.target.response;
                LoadPublishAddons();
                LoadLocalAddons();
            }
        },
    );
}

/**
 * 加载加载项
 */
function LoadAddons() {
    var addonList = document.getElementById("addonList");
    addonList.style.maxWidth = 800 * window.devicePixelRatio + "px";
    var ClearAll = document.getElementById("ClearAll");
    ClearAll.style.maxWidth = 800 * window.devicePixelRatio + "px";
    InitSdk();
}

/**
 * 清除所有加载项
 */
function ClearAll() {
    if (confirm('确定要禁用所有WPS加载项吗？')) {
        var element = {};
        WpsAddonHandleEx(element, 'disableall');
    }
}

// Base64编码相关函数
var fromCharCode = String.fromCharCode;
var cb_utob = function (c) {
    if (c.length < 2) {
        var cc = c.charCodeAt(0);
        return cc < 0x80 ? c :
            cc < 0x800 ? (fromCharCode(0xc0 | (cc >>> 6)) +
                fromCharCode(0x80 | (cc & 0x3f))) :
                (fromCharCode(0xe0 | ((cc >>> 12) & 0x0f)) +
                    fromCharCode(0x80 | ((cc >>> 6) & 0x3f)) +
                    fromCharCode(0x80 | (cc & 0x3f)));
    } else {
        var cc = 0x10000 +
            (c.charCodeAt(0) - 0xD800) * 0x400 +
            (c.charCodeAt(1) - 0xDC00);
        return (fromCharCode(0xf0 | ((cc >>> 18) & 0x07)) +
            fromCharCode(0x80 | ((cc >>> 12) & 0x3f)) +
            fromCharCode(0x80 | ((cc >>> 6) & 0x3f)) +
            fromCharCode(0x80 | (cc & 0x3f)));
    }
};
var re_utob = /[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g;
var utob = function (u) {
    return u.replace(re_utob, cb_utob);
};
var _encode = function (u) {
    var isUint8Array = Object.prototype.toString.call(u) === '[object Uint8Array]';
    if (isUint8Array)
        return u.toString('base64')
    else
        return btoa(utob(String(u)));
}

if (typeof btoa !== 'function') btoa = func_btoa;

function func_btoa(input) {
    var str = String(input);
    var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
    for (
        // initialize result and counter
        var block, charCode, idx = 0, map = chars, output = '';
        // if the next str index does not exist:
        //   change the mapping table to "="
        //   check if d has no fractional digits
        str.charAt(idx | 0) || (map = '=', idx % 1);
        // "8 - idx % 1 * 8" generates the sequence 2, 4, 6, 8
        output += map.charAt(63 & block >> 8 - idx % 1 * 8)
    ) {
        charCode = str.charCodeAt(idx += 3 / 4);
        if (charCode > 0xFF) {
            throw new InvalidCharacterError("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");
        }
        block = block << 8 | charCode;
    }
    return output;
}

function encode(u, urisafe) {
    return !urisafe ?
        _encode(u) :
        _encode(String(u)).replace(/[+\/]/g, function (m0) {
            return m0 == '+' ? '-' : '_';
        }).replace(/=/g, '');
}
