import express from 'express'
import fetch from 'node-fetch'
import OpenAI from 'openai'

const app = express()

// 添加CORS中间件，允许所有跨域请求
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    next();
});

// 提供静态文件服务
app.use(express.static("public"))

// GPUStack API配置
const GPUSTACK_API_URL = "http://10.142.82.25:38080/v1"
const GPUSTACK_API_KEY = "gpustack_a4d04866a7774289_6364f49bbc56af560ecd8829ed9a083a"

// 添加一个API端点，用于测试连接
app.get('/api/status', (req, res) => {
    res.json({ status: 'ok', message: '服务器连接正常' });
});



// 添加GPUStack API代理端点用于翻译功能
app.post('/api/gpustack/translate', express.json(), async (req, res) => {
    try {
        console.log('收到翻译请求');

        // 获取请求数据
        const requestData = req.body;
        console.log('请求数据:', JSON.stringify(requestData, null, 2));

        // 检查是否请求流式输出
        const isStreamRequest = requestData.stream === true;

        // 创建OpenAI客户端实例
        console.log('创建OpenAI客户端实例，连接到GPUStack API');
        const openai = new OpenAI({
            apiKey: "gpustack_a4d04866a7774289_6364f49bbc56af560ecd8829ed9a083a",
            baseURL: "http://10.142.82.25:38080/v1",
            dangerouslyAllowBrowser: true
        });
        console.log('OpenAI客户端实例创建成功');

        // 构建翻译提示
        let prompt = '';
        if (requestData.messages && requestData.messages.length > 0) {
            // 如果提供了messages，使用最后一条用户消息作为翻译内容
            const lastUserMessage = requestData.messages.filter(msg => msg.role === 'user').pop();
            if (lastUserMessage) {
                prompt = `请将以下文本翻译成中文（如果原文是中文则翻译成英文），直接输出翻译结果，不要添加任何解释或格式化指令：\n\n${lastUserMessage.content}`;
            }
        } else if (requestData.text) {
            // 如果提供了text字段，直接使用
            prompt = `请将以下文本翻译成中文（如果原文是中文则翻译成英文），直接输出翻译结果，不要添加任何解释或格式化指令：\n\n${requestData.text}`;
        } else {
            prompt = '请提供需要翻译的文本。';
        }

        // 构建请求参数
        const params = {
            seed: null,
            stop: null,
            temperature: requestData.temperature || 0.3,
            top_p: requestData.top_p || 0.8,
            max_tokens: requestData.max_tokens || 4096,
            frequency_penalty: requestData.frequency_penalty || 0.1,
            presence_penalty: requestData.presence_penalty || 0.1,
            model: "deepseek-r1",
            messages: [
                {
                    role: "user",
                    content: prompt
                }
            ],
            stream: isStreamRequest
        };

        console.log('发送到GPUStack API的请求:', JSON.stringify(params, null, 2));
        console.log('是否使用流式输出:', isStreamRequest);
        console.log('GPUStack API URL:', "http://10.142.82.25:38080/v1");
        console.log('GPUStack API Key:', "gpustack_a4d04866a7774289_6364f49bbc56af560ecd8829ed9a083a");

        // 设置响应头
        if (isStreamRequest) {
            res.setHeader('Content-Type', 'text/event-stream');
            res.setHeader('Cache-Control', 'no-cache');
            res.setHeader('Connection', 'keep-alive');
        }

        try {
            // 调用GPUStack API
            console.log('开始调用GPUStack API...');
            console.log('请求参数:', JSON.stringify(params, null, 2));

            // 添加超时处理
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('GPUStack API请求超时')), 120000); // 增加到120秒超时
            });

            // 添加重试机制
            let response;
            let retryCount = 0;
            const maxRetries = 2;

            while (retryCount <= maxRetries) {
                try {
                    console.log(`尝试调用GPUStack API (尝试 ${retryCount + 1}/${maxRetries + 1})...`);

                    // 使用Promise.race实现超时控制
                    response = await Promise.race([
                        openai.chat.completions.create(params),
                        timeoutPromise
                    ]);

                    // 如果成功，跳出循环
                    break;
                } catch (error) {
                    retryCount++;
                    console.error(`GPUStack API调用失败 (尝试 ${retryCount}/${maxRetries + 1}):`, error.message);

                    // 如果已经达到最大重试次数，抛出错误
                    if (retryCount > maxRetries) {
                        throw error;
                    }

                    // 等待一段时间后重试
                    const retryDelay = 3000 * retryCount; // 递增延迟
                    console.log(`将在 ${retryDelay}ms 后重试...`);
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                }
            }

            console.log('GPUStack API调用成功');
            console.log('响应状态:', response ? '成功' : '无响应');

            // 处理响应
            if (isStreamRequest) {
                // 流式响应处理
                console.log('处理GPUStack API流式响应');

                // 用于收集完整响应内容的变量
                let fullContent = '';
                let lastChunk = null;
                let allChunks = [];

                try {
                    // 遍历流式响应
                    for await (const chunk of response) {
                        console.log('收到流式响应数据块:', JSON.stringify(chunk));
                        lastChunk = chunk;
                        allChunks.push(chunk);

                        // 检查chunk的格式
                        if (chunk && chunk.choices && chunk.choices.length > 0 && chunk.choices[0].delta) {
                            // 提取delta内容
                            const delta = chunk.choices[0].delta || {};

                            // 检查delta是否包含content字段
                            if (delta.content !== undefined) {
                                const content = delta.content || '';
                                // 累积内容
                                fullContent += content;
                            }

                            // 直接将原始数据块发送给客户端
                            res.write(`data: ${JSON.stringify(chunk)}\n\n`);
                        } else {
                            // 其他类型的数据块也发送给客户端
                            res.write(`data: ${JSON.stringify(chunk)}\n\n`);
                        }
                    }

                    // 记录完整的响应内容
                    console.log('完整的响应内容:', fullContent);
                    console.log('收集到的数据块数量:', allChunks.length);

                    // 如果fullContent为空，尝试从所有数据块中重新构建
                    if (!fullContent && allChunks.length > 0) {
                        console.log('尝试从所有数据块中重新构建完整内容');
                        fullContent = allChunks
                            .filter(chunk => chunk.choices && chunk.choices[0] && chunk.choices[0].delta && chunk.choices[0].delta.content)
                            .map(chunk => chunk.choices[0].delta.content)
                            .join('');
                        console.log('重新构建的完整内容:', fullContent);
                    }

                    // 如果仍然为空，尝试从最后一个数据块中提取
                    if (!fullContent && lastChunk && lastChunk.choices && lastChunk.choices[0]) {
                        if (lastChunk.choices[0].message && lastChunk.choices[0].message.content) {
                            fullContent = lastChunk.choices[0].message.content;
                            console.log('从最后一个数据块的message中提取的内容:', fullContent);
                        } else if (lastChunk.choices[0].text) {
                            fullContent = lastChunk.choices[0].text;
                            console.log('从最后一个数据块的text中提取的内容:', fullContent);
                        }
                    }

                    // 不再需要发送最终完整结果数据包，因为前端已经实现了增量更新
                    // 只在没有提取到任何内容时发送错误信息
                    if (!fullContent) {
                        console.error('无法提取完整的响应内容');
                        // 发送错误信息
                        const errorResult = {
                            error: {
                                message: "无法提取完整的响应内容",
                                type: "processing_error",
                                param: null,
                                code: "content_extraction_failed"
                            }
                        };
                        res.write(`data: ${JSON.stringify(errorResult)}\n\n`);
                    }
                } catch (error) {
                    console.error('处理流式响应时出错:', error);
                    res.write(`data: ${JSON.stringify({ error: '处理流式响应时出错', message: error.message })}\n\n`);
                } finally {
                    // 确保在所有情况下都发送结束标记
                    console.log('流式响应结束，发送[DONE]标记');
                    res.write('data: [DONE]\n\n');
                    res.end();
                }
            } else {
                // 非流式响应处理
                console.log('GPUStack API响应类型:', typeof response);
                console.log('GPUStack API响应结构:', Object.keys(response));

                try {
                    // 尝试提取响应内容
                    if (response.choices && response.choices.length > 0) {
                        const content = response.choices[0].message?.content;
                        console.log('GPUStack API响应内容:', content ? content.substring(0, 100) + '...' : 'null');
                    }

                    // 完整响应日志（限制长度）
                    const responseStr = JSON.stringify(response);
                    console.log('GPUStack API完整响应:', responseStr.length > 500 ? responseStr.substring(0, 500) + '...' : responseStr);

                    // 检查响应格式是否需要适配为OpenAI格式
                    let openaiCompatibleResponse = response;

                    // 如果响应不符合OpenAI格式，进行转换
                    if (response && !response.object) {
                        openaiCompatibleResponse = {
                            id: response.id || `chatcmpl-${Date.now()}`,
                            object: "chat.completion",
                            created: response.created || Math.floor(Date.now() / 1000),
                            model: response.model || "deepseek-r1",
                            choices: response.choices || [],
                            usage: response.usage || {
                                prompt_tokens: 0,
                                completion_tokens: 0,
                                total_tokens: 0
                            }
                        };

                        // 确保choices格式正确
                        if (openaiCompatibleResponse.choices && openaiCompatibleResponse.choices.length > 0) {
                            // 确保每个choice都有正确的格式
                            openaiCompatibleResponse.choices = openaiCompatibleResponse.choices.map((choice, index) => {
                                return {
                                    index: index,
                                    message: choice.message || { role: "assistant", content: choice.text || "" },
                                    finish_reason: choice.finish_reason || "stop"
                                };
                            });
                        }

                        console.log('已将GPUStack API响应适配为OpenAI格式');
                    }

                    // 返回适配后的响应数据
                    res.json(openaiCompatibleResponse);
                } catch (e) {
                    console.error('解析GPUStack API响应时出错:', e);
                    // 出错时尝试返回原始响应
                    res.json(response);
                }
            }
        } catch (error) {
            console.error('GPUStack API请求失败:', error);
            console.error('错误详情:', error.stack);

            // 提取更详细的错误信息
            let errorMessage = error.message || '未知错误';
            let errorDetails = {};

            // 尝试解析错误对象中的更多信息
            if (error.response) {
                try {
                    errorDetails = {
                        status: error.response.status,
                        statusText: error.response.statusText,
                        data: error.response.data
                    };
                    console.error('API错误响应详情:', JSON.stringify(errorDetails, null, 2));
                } catch (e) {
                    console.error('无法解析错误响应:', e);
                }
            }

            // 构建OpenAI兼容的错误响应
            const openaiCompatibleError = {
                error: {
                    message: `GPUStack API请求失败: ${errorMessage}`,
                    type: "api_error",
                    param: null,
                    code: error.code || "unknown_error",
                    details: errorDetails
                }
            };

            if (isStreamRequest) {
                // 对于流式请求，发送错误信息并结束流
                res.write(`data: ${JSON.stringify(openaiCompatibleError)}\n\n`);
                res.end('data: [DONE]\n\n');
            } else {
                // 对于非流式请求，返回错误JSON
                res.status(500).json(openaiCompatibleError);
            }
        }
    } catch (error) {
        console.error('翻译代理错误:', error);
        console.error('错误详情:', error.stack);

        // 构建OpenAI兼容的错误响应
        const openaiCompatibleError = {
            error: {
                message: `翻译代理错误: ${error.message || '未知错误'}`,
                type: "server_error",
                param: null,
                code: "proxy_error",
                details: {
                    stack: error.stack
                }
            }
        };

        // 检查是否是流式请求
        if (req.body && req.body.stream === true) {
            if (!res.writableEnded) {
                res.write(`data: ${JSON.stringify(openaiCompatibleError)}\n\n`);
                res.end('data: [DONE]\n\n');
            }
        } else {
            res.status(500).json(openaiCompatibleError);
        }
    }
});



// 添加GPUStack API代理端点用于智能助手功能
app.post('/api/gpustack/assistant', express.json(), async (req, res) => {
    try {
        console.log('收到智能助手请求');

        // 获取请求数据
        const requestData = req.body;
        console.log('请求数据:', JSON.stringify(requestData, null, 2));

        // 检查是否请求流式输出
        const isStreamRequest = requestData.stream === true;

        // 创建OpenAI客户端实例
        console.log('创建OpenAI客户端实例，连接到GPUStack API');
        const openai = new OpenAI({
            apiKey: "gpustack_a4d04866a7774289_6364f49bbc56af560ecd8829ed9a083a",
            baseURL: "http://10.142.82.25:38080/v1",
            dangerouslyAllowBrowser: true
        });
        console.log('OpenAI客户端实例创建成功');

        // 设置响应头
        if (isStreamRequest) {
            res.setHeader('Content-Type', 'text/event-stream');
            res.setHeader('Cache-Control', 'no-cache');
            res.setHeader('Connection', 'keep-alive');
        }

        try {
            // 调用GPUStack API
            console.log('开始调用GPUStack API...');
            console.log('请求参数:', JSON.stringify(requestData, null, 2));

            // 添加超时处理
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('GPUStack API请求超时')), 120000); // 120秒超时
            });

            // 添加重试机制
            let response;
            let retryCount = 0;
            const maxRetries = 2;

            while (retryCount <= maxRetries) {
                try {
                    console.log(`尝试调用GPUStack API (尝试 ${retryCount + 1}/${maxRetries + 1})...`);

                    // 使用Promise.race实现超时控制
                    response = await Promise.race([
                        openai.chat.completions.create(requestData),
                        timeoutPromise
                    ]);

                    // 如果成功，跳出循环
                    break;
                } catch (error) {
                    retryCount++;
                    console.error(`GPUStack API调用失败 (尝试 ${retryCount}/${maxRetries + 1}):`, error.message);

                    // 如果已经达到最大重试次数，抛出错误
                    if (retryCount > maxRetries) {
                        throw error;
                    }

                    // 等待一段时间后重试
                    const retryDelay = 3000 * retryCount; // 递增延迟
                    console.log(`将在 ${retryDelay}ms 后重试...`);
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                }
            }

            console.log('GPUStack API调用成功');
            console.log('响应状态:', response ? '成功' : '无响应');

            // 处理响应
            if (isStreamRequest) {
                // 流式响应处理
                console.log('处理GPUStack API流式响应');

                // 用于收集完整响应内容的变量
                let fullContent = '';
                let lastChunk = null;
                let allChunks = [];

                try {
                    // 遍历流式响应
                    for await (const chunk of response) {
                        console.log('收到流式响应数据块:', JSON.stringify(chunk));
                        lastChunk = chunk;
                        allChunks.push(chunk);

                        // 检查chunk的格式
                        if (chunk && chunk.choices && chunk.choices.length > 0 && chunk.choices[0].delta) {
                            // 提取delta内容
                            const delta = chunk.choices[0].delta || {};

                            // 检查delta是否包含content字段
                            if (delta.content !== undefined) {
                                const content = delta.content || '';
                                // 累积内容
                                fullContent += content;
                            }

                            // 直接将原始数据块发送给客户端
                            res.write(`data: ${JSON.stringify(chunk)}\n\n`);
                        } else {
                            // 其他类型的数据块也发送给客户端
                            res.write(`data: ${JSON.stringify(chunk)}\n\n`);
                        }
                    }

                    // 记录完整的响应内容
                    console.log('完整的响应内容长度:', fullContent.length);
                    console.log('收集到的数据块数量:', allChunks.length);

                    // 如果fullContent为空，尝试从所有数据块中重新构建
                    if (!fullContent && allChunks.length > 0) {
                        console.log('尝试从所有数据块中重新构建完整内容');
                        fullContent = allChunks
                            .filter(chunk => chunk.choices && chunk.choices[0] && chunk.choices[0].delta && chunk.choices[0].delta.content)
                            .map(chunk => chunk.choices[0].delta.content)
                            .join('');
                        console.log('重新构建的完整内容长度:', fullContent.length);
                    }

                    // 如果仍然为空，尝试从最后一个数据块中提取
                    if (!fullContent && lastChunk && lastChunk.choices && lastChunk.choices[0]) {
                        if (lastChunk.choices[0].message && lastChunk.choices[0].message.content) {
                            fullContent = lastChunk.choices[0].message.content;
                            console.log('从最后一个数据块的message中提取的内容长度:', fullContent.length);
                        } else if (lastChunk.choices[0].text) {
                            fullContent = lastChunk.choices[0].text;
                            console.log('从最后一个数据块的text中提取的内容长度:', fullContent.length);
                        }
                    }

                    // 不再需要发送最终完整结果数据包，因为前端已经实现了增量更新
                    // 只在没有提取到任何内容时发送错误信息
                    if (!fullContent) {
                        console.error('无法提取完整的响应内容');
                        // 发送错误信息
                        const errorResult = {
                            error: {
                                message: "无法提取完整的响应内容",
                                type: "processing_error",
                                param: null,
                                code: "content_extraction_failed"
                            }
                        };
                        res.write(`data: ${JSON.stringify(errorResult)}\n\n`);
                    }
                } catch (error) {
                    console.error('处理流式响应时出错:', error);
                    res.write(`data: ${JSON.stringify({ error: '处理流式响应时出错', message: error.message })}\n\n`);
                } finally {
                    // 确保在所有情况下都发送结束标记
                    console.log('流式响应结束，发送[DONE]标记');
                    res.write('data: [DONE]\n\n');
                    res.end();
                }
            } else {
                // 非流式响应处理
                console.log('GPUStack API响应类型:', typeof response);
                console.log('GPUStack API响应结构:', Object.keys(response));

                try {
                    // 尝试提取响应内容
                    if (response.choices && response.choices.length > 0) {
                        const content = response.choices[0].message?.content;
                        console.log('GPUStack API响应内容长度:', content ? content.length : 0);
                    }

                    // 完整响应日志（限制长度）
                    const responseStr = JSON.stringify(response);
                    console.log('GPUStack API完整响应长度:', responseStr.length);

                    // 返回响应数据
                    res.json(response);
                } catch (e) {
                    console.error('解析GPUStack API响应时出错:', e);
                    // 出错时尝试返回原始响应
                    res.json(response);
                }
            }
        } catch (error) {
            console.error('GPUStack API请求失败:', error);
            console.error('错误详情:', error.stack);

            // 提取更详细的错误信息
            let errorMessage = error.message || '未知错误';
            let errorDetails = {};

            // 尝试解析错误对象中的更多信息
            if (error.response) {
                try {
                    errorDetails = {
                        status: error.response.status,
                        statusText: error.response.statusText,
                        data: error.response.data
                    };
                    console.error('API错误响应详情:', JSON.stringify(errorDetails, null, 2));
                } catch (e) {
                    console.error('无法解析错误响应:', e);
                }
            }

            // 构建OpenAI兼容的错误响应
            const openaiCompatibleError = {
                error: {
                    message: `GPUStack API请求失败: ${errorMessage}`,
                    type: "api_error",
                    param: null,
                    code: error.code || "unknown_error",
                    details: errorDetails
                }
            };

            if (isStreamRequest) {
                // 对于流式请求，发送错误信息并结束流
                res.write(`data: ${JSON.stringify(openaiCompatibleError)}\n\n`);
                res.end('data: [DONE]\n\n');
            } else {
                // 对于非流式请求，返回错误JSON
                res.status(500).json(openaiCompatibleError);
            }
        }
    } catch (error) {
        console.error('智能助手代理错误:', error);
        console.error('错误详情:', error.stack);

        // 构建OpenAI兼容的错误响应
        const openaiCompatibleError = {
            error: {
                message: `智能助手代理错误: ${error.message || '未知错误'}`,
                type: "server_error",
                param: null,
                code: "proxy_error",
                details: {
                    stack: error.stack
                }
            }
        };

        // 检查是否是流式请求
        if (req.body && req.body.stream === true) {
            if (!res.writableEnded) {
                res.write(`data: ${JSON.stringify(openaiCompatibleError)}\n\n`);
                res.end('data: [DONE]\n\n');
            }
        } else {
            res.status(500).json(openaiCompatibleError);
        }
    }
});

// 添加GPUStack API代理端点用于专利申请交底书功能
app.post('/api/gpustack/patent', express.json(), async (req, res) => {
    try {
        console.log('收到专利申请交底书生成请求');

        // 获取请求数据
        const requestData = req.body;
        console.log('请求数据:', JSON.stringify(requestData, null, 2));

        // 检查是否请求流式输出
        const isStreamRequest = requestData.stream === true;

        // 创建OpenAI客户端实例
        console.log('创建OpenAI客户端实例，连接到GPUStack API');
        const openai = new OpenAI({
            apiKey: "gpustack_a4d04866a7774289_6364f49bbc56af560ecd8829ed9a083a",
            baseURL: "http://10.142.82.25:38080/v1",
            dangerouslyAllowBrowser: true
        });
        console.log('OpenAI客户端实例创建成功');

        // 设置响应头
        if (isStreamRequest) {
            res.setHeader('Content-Type', 'text/event-stream');
            res.setHeader('Cache-Control', 'no-cache');
            res.setHeader('Connection', 'keep-alive');
        }

        try {
            // 调用GPUStack API
            console.log('开始调用GPUStack API...');
            console.log('请求参数:', JSON.stringify(requestData, null, 2));

            // 添加超时处理
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('GPUStack API请求超时')), 120000); // 120秒超时
            });

            // 添加重试机制
            let response;
            let retryCount = 0;
            const maxRetries = 2;

            while (retryCount <= maxRetries) {
                try {
                    console.log(`尝试调用GPUStack API (尝试 ${retryCount + 1}/${maxRetries + 1})...`);

                    // 使用Promise.race实现超时控制
                    response = await Promise.race([
                        openai.chat.completions.create(requestData),
                        timeoutPromise
                    ]);

                    // 如果成功，跳出循环
                    break;
                } catch (error) {
                    retryCount++;
                    console.error(`GPUStack API调用失败 (尝试 ${retryCount}/${maxRetries + 1}):`, error.message);

                    // 如果已经达到最大重试次数，抛出错误
                    if (retryCount > maxRetries) {
                        throw error;
                    }

                    // 等待一段时间后重试
                    const retryDelay = 3000 * retryCount; // 递增延迟
                    console.log(`将在 ${retryDelay}ms 后重试...`);
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                }
            }

            console.log('GPUStack API调用成功');
            console.log('响应状态:', response ? '成功' : '无响应');

            // 处理响应
            if (isStreamRequest) {
                // 流式响应处理
                console.log('处理GPUStack API流式响应');

                // 用于收集完整响应内容的变量
                let fullContent = '';
                let lastChunk = null;
                let allChunks = [];

                try {
                    // 遍历流式响应
                    for await (const chunk of response) {
                        console.log('收到流式响应数据块:', JSON.stringify(chunk));
                        lastChunk = chunk;
                        allChunks.push(chunk);

                        // 检查chunk的格式
                        if (chunk && chunk.choices && chunk.choices.length > 0 && chunk.choices[0].delta) {
                            // 提取delta内容
                            const delta = chunk.choices[0].delta || {};

                            // 检查delta是否包含content字段
                            if (delta.content !== undefined) {
                                const content = delta.content || '';
                                // 累积内容
                                fullContent += content;
                            }

                            // 直接将原始数据块发送给客户端
                            res.write(`data: ${JSON.stringify(chunk)}\n\n`);
                        } else {
                            // 其他类型的数据块也发送给客户端
                            res.write(`data: ${JSON.stringify(chunk)}\n\n`);
                        }
                    }

                    // 记录完整的响应内容
                    console.log('完整的响应内容长度:', fullContent.length);
                    console.log('收集到的数据块数量:', allChunks.length);

                    // 如果fullContent为空，尝试从所有数据块中重新构建
                    if (!fullContent && allChunks.length > 0) {
                        console.log('尝试从所有数据块中重新构建完整内容');
                        fullContent = allChunks
                            .filter(chunk => chunk.choices && chunk.choices[0] && chunk.choices[0].delta && chunk.choices[0].delta.content)
                            .map(chunk => chunk.choices[0].delta.content)
                            .join('');
                        console.log('重新构建的完整内容长度:', fullContent.length);
                    }

                    // 如果仍然为空，尝试从最后一个数据块中提取
                    if (!fullContent && lastChunk && lastChunk.choices && lastChunk.choices[0]) {
                        if (lastChunk.choices[0].message && lastChunk.choices[0].message.content) {
                            fullContent = lastChunk.choices[0].message.content;
                            console.log('从最后一个数据块的message中提取的内容长度:', fullContent.length);
                        } else if (lastChunk.choices[0].text) {
                            fullContent = lastChunk.choices[0].text;
                            console.log('从最后一个数据块的text中提取的内容长度:', fullContent.length);
                        }
                    }

                    // 不再需要发送最终完整结果数据包，因为前端已经实现了增量更新
                    // 只在没有提取到任何内容时发送错误信息
                    if (!fullContent) {
                        console.error('无法提取完整的响应内容');
                        // 发送错误信息
                        const errorResult = {
                            error: {
                                message: "无法提取完整的响应内容",
                                type: "processing_error",
                                param: null,
                                code: "content_extraction_failed"
                            }
                        };
                        res.write(`data: ${JSON.stringify(errorResult)}\n\n`);
                    }
                } catch (error) {
                    console.error('处理流式响应时出错:', error);
                    res.write(`data: ${JSON.stringify({ error: '处理流式响应时出错', message: error.message })}\n\n`);
                } finally {
                    // 确保在所有情况下都发送结束标记
                    console.log('流式响应结束，发送[DONE]标记');
                    res.write('data: [DONE]\n\n');
                    res.end();
                }
            } else {
                // 非流式响应处理
                console.log('GPUStack API响应类型:', typeof response);
                console.log('GPUStack API响应结构:', Object.keys(response));

                try {
                    // 尝试提取响应内容
                    if (response.choices && response.choices.length > 0) {
                        const content = response.choices[0].message?.content;
                        console.log('GPUStack API响应内容长度:', content ? content.length : 0);
                    }

                    // 完整响应日志（限制长度）
                    const responseStr = JSON.stringify(response);
                    console.log('GPUStack API完整响应长度:', responseStr.length);

                    // 返回响应数据
                    res.json(response);
                } catch (e) {
                    console.error('解析GPUStack API响应时出错:', e);
                    // 出错时尝试返回原始响应
                    res.json(response);
                }
            }
        } catch (error) {
            console.error('GPUStack API请求失败:', error);
            console.error('错误详情:', error.stack);

            // 提取更详细的错误信息
            let errorMessage = error.message || '未知错误';
            let errorDetails = {};

            // 尝试解析错误对象中的更多信息
            if (error.response) {
                try {
                    errorDetails = {
                        status: error.response.status,
                        statusText: error.response.statusText,
                        data: error.response.data
                    };
                    console.error('API错误响应详情:', JSON.stringify(errorDetails, null, 2));
                } catch (e) {
                    console.error('无法解析错误响应:', e);
                }
            }

            // 构建OpenAI兼容的错误响应
            const openaiCompatibleError = {
                error: {
                    message: `GPUStack API请求失败: ${errorMessage}`,
                    type: "api_error",
                    param: null,
                    code: error.code || "unknown_error",
                    details: errorDetails
                }
            };

            if (isStreamRequest) {
                // 对于流式请求，发送错误信息并结束流
                res.write(`data: ${JSON.stringify(openaiCompatibleError)}\n\n`);
                res.end('data: [DONE]\n\n');
            } else {
                // 对于非流式请求，返回错误JSON
                res.status(500).json(openaiCompatibleError);
            }
        }
    } catch (error) {
        console.error('专利申请交底书代理错误:', error);
        console.error('错误详情:', error.stack);

        // 构建OpenAI兼容的错误响应
        const openaiCompatibleError = {
            error: {
                message: `专利申请交底书代理错误: ${error.message || '未知错误'}`,
                type: "server_error",
                param: null,
                code: "proxy_error",
                details: {
                    stack: error.stack
                }
            }
        };

        // 检查是否是流式请求
        if (req.body && req.body.stream === true) {
            if (!res.writableEnded) {
                res.write(`data: ${JSON.stringify(openaiCompatibleError)}\n\n`);
                res.end('data: [DONE]\n\n');
            }
        } else {
            res.status(500).json(openaiCompatibleError);
        }
    }
});

// 添加GPUStack API代理端点用于文字润色功能
app.post('/api/polish', express.json(), async (req, res) => {
    try {
        console.log('收到文字润色请求');

        // 获取请求数据
        const requestData = req.body;
        console.log('请求数据:', JSON.stringify(requestData, null, 2));

        // 检查是否请求流式输出
        const isStreamRequest = requestData.stream === true;

        // 创建OpenAI客户端实例
        console.log('创建OpenAI客户端实例，连接到GPUStack API');
        const openai = new OpenAI({
            apiKey: "gpustack_a4d04866a7774289_6364f49bbc56af560ecd8829ed9a083a",
            baseURL: "http://10.142.82.25:38080/v1",
            dangerouslyAllowBrowser: true
        });
        console.log('OpenAI客户端实例创建成功');

        // 构建请求参数
        const params = {
            seed: null,
            stop: null,
            temperature: requestData.temperature || 0.3,
            top_p: requestData.top_p || 0.8,
            max_tokens: requestData.max_tokens || 4096,
            frequency_penalty: requestData.frequency_penalty || 0.1,
            presence_penalty: requestData.presence_penalty || 0.1,
            model: "deepseek-r1",
            messages: requestData.messages || [],
            stream: isStreamRequest
        };

        console.log('发送到GPUStack API的请求:', JSON.stringify(params, null, 2));
        console.log('是否使用流式输出:', isStreamRequest);
        console.log('GPUStack API URL:', "http://10.142.82.25:38080/v1");
        console.log('GPUStack API Key:', "gpustack_a4d04866a7774289_6364f49bbc56af560ecd8829ed9a083a");

        // 设置响应头
        if (isStreamRequest) {
            res.setHeader('Content-Type', 'text/event-stream');
            res.setHeader('Cache-Control', 'no-cache');
            res.setHeader('Connection', 'keep-alive');
        }

        try {
            // 调用GPUStack API
            console.log('开始调用GPUStack API...');
            console.log('请求参数:', JSON.stringify(params, null, 2));

            // 添加超时处理
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('GPUStack API请求超时')), 120000); // 增加到120秒超时
            });

            // 添加重试机制
            let response;
            let retryCount = 0;
            const maxRetries = 2;

            while (retryCount <= maxRetries) {
                try {
                    console.log(`尝试调用GPUStack API (尝试 ${retryCount + 1}/${maxRetries + 1})...`);

                    // 使用Promise.race实现超时控制
                    response = await Promise.race([
                        openai.chat.completions.create(params),
                        timeoutPromise
                    ]);

                    // 如果成功，跳出循环
                    break;
                } catch (error) {
                    retryCount++;
                    console.error(`GPUStack API调用失败 (尝试 ${retryCount}/${maxRetries + 1}):`, error.message);

                    // 如果已经达到最大重试次数，抛出错误
                    if (retryCount > maxRetries) {
                        throw error;
                    }

                    // 等待一段时间后重试
                    const retryDelay = 3000 * retryCount; // 递增延迟
                    console.log(`将在 ${retryDelay}ms 后重试...`);
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                }
            }

            console.log('GPUStack API调用成功');
            console.log('响应状态:', response ? '成功' : '无响应');

            // 处理响应
            if (isStreamRequest) {
                // 流式响应处理
                console.log('处理GPUStack API流式响应');

                // 用于收集完整响应内容的变量
                let fullContent = '';
                let lastChunk = null;
                let allChunks = [];

                try {
                    // 遍历流式响应
                    for await (const chunk of response) {
                        console.log('收到流式响应数据块:', JSON.stringify(chunk));
                        lastChunk = chunk;
                        allChunks.push(chunk);

                        // 检查chunk的格式
                        if (chunk && chunk.choices && chunk.choices.length > 0 && chunk.choices[0].delta) {
                            // 提取delta内容
                            const delta = chunk.choices[0].delta || {};

                            // 检查delta是否包含content字段
                            if (delta.content !== undefined) {
                                const content = delta.content || '';
                                // 累积内容
                                fullContent += content;
                            }

                            // 直接将原始数据块发送给客户端
                            res.write(`data: ${JSON.stringify(chunk)}\n\n`);
                        } else {
                            // 其他类型的数据块也发送给客户端
                            res.write(`data: ${JSON.stringify(chunk)}\n\n`);
                        }
                    }

                    // 记录完整的响应内容
                    console.log('完整的响应内容:', fullContent);
                    console.log('收集到的数据块数量:', allChunks.length);

                    // 如果fullContent为空，尝试从所有数据块中重新构建
                    if (!fullContent && allChunks.length > 0) {
                        console.log('尝试从所有数据块中重新构建完整内容');
                        fullContent = allChunks
                            .filter(chunk => chunk.choices && chunk.choices[0] && chunk.choices[0].delta && chunk.choices[0].delta.content)
                            .map(chunk => chunk.choices[0].delta.content)
                            .join('');
                        console.log('重新构建的完整内容:', fullContent);
                    }

                    // 如果仍然为空，尝试从最后一个数据块中提取
                    if (!fullContent && lastChunk && lastChunk.choices && lastChunk.choices[0]) {
                        if (lastChunk.choices[0].message && lastChunk.choices[0].message.content) {
                            fullContent = lastChunk.choices[0].message.content;
                            console.log('从最后一个数据块的message中提取的内容:', fullContent);
                        } else if (lastChunk.choices[0].text) {
                            fullContent = lastChunk.choices[0].text;
                            console.log('从最后一个数据块的text中提取的内容:', fullContent);
                        }
                    }

                    // 不再需要发送最终完整结果数据包，因为前端已经实现了增量更新
                    // 只在没有提取到任何内容时发送错误信息
                    if (!fullContent) {
                        console.error('无法提取完整的响应内容');
                        // 发送错误信息
                        const errorResult = {
                            error: {
                                message: "无法提取完整的响应内容",
                                type: "processing_error",
                                param: null,
                                code: "content_extraction_failed"
                            }
                        };
                        res.write(`data: ${JSON.stringify(errorResult)}\n\n`);
                    }
                } catch (error) {
                    console.error('处理流式响应时出错:', error);
                    res.write(`data: ${JSON.stringify({ error: '处理流式响应时出错', message: error.message })}\n\n`);
                } finally {
                    // 确保在所有情况下都发送结束标记
                    console.log('流式响应结束，发送[DONE]标记');
                    res.write('data: [DONE]\n\n');
                    res.end();
                }
            } else {
                // 非流式响应处理
                console.log('GPUStack API响应类型:', typeof response);
                console.log('GPUStack API响应结构:', Object.keys(response));

                try {
                    // 尝试提取响应内容
                    if (response.choices && response.choices.length > 0) {
                        const content = response.choices[0].message?.content;
                        console.log('GPUStack API响应内容:', content ? content.substring(0, 100) + '...' : 'null');
                    }

                    // 完整响应日志（限制长度）
                    const responseStr = JSON.stringify(response);
                    console.log('GPUStack API完整响应:', responseStr.length > 500 ? responseStr.substring(0, 500) + '...' : responseStr);

                    // 检查响应格式是否需要适配为OpenAI格式
                    let openaiCompatibleResponse = response;

                    // 如果响应不符合OpenAI格式，进行转换
                    if (response && !response.object) {
                        openaiCompatibleResponse = {
                            id: response.id || `chatcmpl-${Date.now()}`,
                            object: "chat.completion",
                            created: response.created || Math.floor(Date.now() / 1000),
                            model: response.model || "deepseek-r1",
                            choices: response.choices || [],
                            usage: response.usage || {
                                prompt_tokens: 0,
                                completion_tokens: 0,
                                total_tokens: 0
                            }
                        };

                        // 确保choices格式正确
                        if (openaiCompatibleResponse.choices && openaiCompatibleResponse.choices.length > 0) {
                            // 确保每个choice都有正确的格式
                            openaiCompatibleResponse.choices = openaiCompatibleResponse.choices.map((choice, index) => {
                                return {
                                    index: index,
                                    message: choice.message || { role: "assistant", content: choice.text || "" },
                                    finish_reason: choice.finish_reason || "stop"
                                };
                            });
                        }

                        console.log('已将GPUStack API响应适配为OpenAI格式');
                    }

                    // 返回适配后的响应数据
                    res.json(openaiCompatibleResponse);
                } catch (e) {
                    console.error('解析GPUStack API响应时出错:', e);
                    // 出错时尝试返回原始响应
                    res.json(response);
                }
            }
        } catch (error) {
            console.error('GPUStack API请求失败:', error);
            console.error('错误详情:', error.stack);

            // 提取更详细的错误信息
            let errorMessage = error.message || '未知错误';
            let errorDetails = {};

            // 尝试解析错误对象中的更多信息
            if (error.response) {
                try {
                    errorDetails = {
                        status: error.response.status,
                        statusText: error.response.statusText,
                        data: error.response.data
                    };
                    console.error('API错误响应详情:', JSON.stringify(errorDetails, null, 2));
                } catch (e) {
                    console.error('无法解析错误响应:', e);
                }
            }

            // 构建OpenAI兼容的错误响应
            const openaiCompatibleError = {
                error: {
                    message: `GPUStack API请求失败: ${errorMessage}`,
                    type: "api_error",
                    param: null,
                    code: error.code || "unknown_error",
                    details: errorDetails
                }
            };

            if (isStreamRequest) {
                // 对于流式请求，发送错误信息并结束流
                res.write(`data: ${JSON.stringify(openaiCompatibleError)}\n\n`);
                res.end('data: [DONE]\n\n');
            } else {
                // 对于非流式请求，返回错误JSON
                res.status(500).json(openaiCompatibleError);
            }
        }
    } catch (error) {
        console.error('文字润色代理错误:', error);
        console.error('错误详情:', error.stack);

        // 构建OpenAI兼容的错误响应
        const openaiCompatibleError = {
            error: {
                message: `文字润色代理错误: ${error.message || '未知错误'}`,
                type: "server_error",
                param: null,
                code: "proxy_error",
                details: {
                    stack: error.stack
                }
            }
        };

        // 检查是否是流式请求
        if (req.body && req.body.stream === true) {
            if (!res.writableEnded) {
                res.write(`data: ${JSON.stringify(openaiCompatibleError)}\n\n`);
                res.end('data: [DONE]\n\n');
            }
        } else {
            res.status(500).json(openaiCompatibleError);
        }
    }
});

// 添加代理端点，将请求转发到通义千问服务器
app.post('/api/proxy/generate', async (req, res) => {
    try {
        console.log('收到代理请求，转发到通义千问服务器');

        // 从请求中获取原始数据
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });

        req.on('end', async () => {
            try {
                // 转发请求到DeepSeek服务器
                const response = await fetch('http://10.142.82.66:22434/api/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: body
                });

                // 如果是流式响应，需要特殊处理
                if (body.includes('"stream":true')) {
                    // 设置响应头
                    res.setHeader('Content-Type', 'application/json');

                    // 创建响应流
                    const stream = response.body;
                    stream.on('data', chunk => {
                        res.write(chunk);
                    });

                    stream.on('end', () => {
                        res.end();
                    });

                    stream.on('error', error => {
                        console.error('流处理错误:', error);
                        res.status(500).end();
                    });
                } else {
                    // 非流式响应，直接返回结果
                    const data = await response.json();
                    res.json(data);
                }
            } catch (error) {
                console.error('代理请求失败:', error);
                res.status(500).json({ error: '代理请求失败: ' + error.message });
            }
        });
    } catch (error) {
        console.error('代理请求处理错误:', error);
        res.status(500).json({ error: '代理请求处理错误: ' + error.message });
    }
});

// 设置全局错误处理
process.on('uncaughtException', (err) => {
    console.error('未捕获的异常:', err);
    console.error(err.stack);
});

process.on('unhandledRejection', (reason) => {
    console.error('未处理的Promise拒绝:', reason);
    if (reason && reason.stack) {
        console.error(reason.stack);
    }
});

// 监听所有网络接口，而不仅仅是localhost
app.listen(3000, '0.0.0.0', () => {
    console.log("服务器已启动，监听端口3000");
    console.log("翻译功能代理端点: http://localhost:3000/api/gpustack/translate");
    console.log("服务器状态端点: http://localhost:3000/api/status");
    console.log("文字润色代理端点: http://localhost:3000/api/polish");
    console.log("智能助手代理端点: http://localhost:3000/api/gpustack/assistant");
    console.log("专利申请交底书代理端点: http://localhost:3000/api/gpustack/patent");
    console.log("通义千问代理端点: http://localhost:3000/api/proxy/generate");
});